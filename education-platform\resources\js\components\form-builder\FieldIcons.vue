<template>
  <component :is="iconComponent" v-bind="$attrs" />
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  type: string;
}

const props = defineProps<Props>();

const iconComponent = computed(() => {
  switch (props.type) {
    case 'TextIcon':
      return 'svg';
    case 'TextAreaIcon':
      return 'svg';
    case 'NumberIcon':
      return 'svg';
    case 'SelectIcon':
      return 'svg';
    case 'CheckboxIcon':
      return 'svg';
    case 'RadioIcon':
      return 'svg';
    case 'DateIcon':
      return 'svg';
    case 'FileIcon':
      return 'svg';
    default:
      return 'svg';
  }
});
</script>

<!-- 文本图标 -->
<template v-if="type === 'TextIcon'">
  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
  </svg>
</template>

<!-- 多行文本图标 -->
<template v-if="type === 'TextAreaIcon'">
  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
  </svg>
</template>

<!-- 数字图标 -->
<template v-if="type === 'NumberIcon'">
  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
  </svg>
</template>

<!-- 下拉选择图标 -->
<template v-if="type === 'SelectIcon'">
  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
  </svg>
</template>

<!-- 多选框图标 -->
<template v-if="type === 'CheckboxIcon'">
  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
</template>

<!-- 单选框图标 -->
<template v-if="type === 'RadioIcon'">
  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
</template>

<!-- 日期图标 -->
<template v-if="type === 'DateIcon'">
  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
</template>

<!-- 文件上传图标 -->
<template v-if="type === 'FileIcon'">
  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
  </svg>
</template>
