<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <svg class="animate-spin h-8 w-8 text-blue-600 mx-auto" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="mt-2 text-sm text-gray-600">加载中...</p>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 导航栏 -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center space-x-4">
              <router-link to="/form-templates" class="text-indigo-600 hover:text-indigo-500">
                ← 返回模板列表
              </router-link>
              <h1 class="text-xl font-semibold text-gray-900">
                查看表单模板 - {{ template.name }}
              </h1>
            </div>
            <div class="flex items-center space-x-4">
              <router-link
                :to="`/form-templates/${template.id}/edit`"
                class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <span>编辑模板</span>
              </router-link>
              <button
                @click="duplicateTemplate"
                :disabled="duplicating"
                class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2"
              >
                <svg v-if="duplicating" class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <span>{{ duplicating ? '复制中...' : '复制模板' }}</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- 左侧：模板信息 -->
          <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">模板信息</h3>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">模板名称</label>
                  <p class="mt-1 text-sm text-gray-900">{{ template.name }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700">分类</label>
                  <p class="mt-1 text-sm text-gray-900">{{ template.category }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700">描述</label>
                  <p class="mt-1 text-sm text-gray-900">{{ template.description || '无描述' }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700">状态</label>
                  <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="template.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'">
                    {{ template.status === 'published' ? '已发布' : '草稿' }}
                  </span>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700">字段数量</label>
                  <p class="mt-1 text-sm text-gray-900">{{ template.config?.fields?.length || 0 }} 个字段</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700">创建时间</label>
                  <p class="mt-1 text-sm text-gray-900">{{ formatDate(template.created_at) }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700">更新时间</label>
                  <p class="mt-1 text-sm text-gray-900">{{ formatDate(template.updated_at) }}</p>
                </div>
              </div>
            </div>

            <!-- 字段统计 -->
            <div class="mt-6 bg-white rounded-lg shadow p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">字段统计</h3>
              
              <div class="space-y-3">
                <div v-for="stat in fieldStats" :key="stat.type" class="flex justify-between items-center">
                  <span class="text-sm text-gray-600">{{ stat.label }}</span>
                  <span class="text-sm font-medium text-gray-900">{{ stat.count }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：表单预览 -->
          <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow">
              <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">表单预览</h3>
                <p class="text-sm text-gray-500 mt-1">以下是表单的实际显示效果</p>
              </div>
              
              <div class="p-6">
                <div v-if="!template.config?.fields?.length" class="text-center py-12">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-900">暂无字段</h3>
                  <p class="mt-1 text-sm text-gray-500">该模板还没有配置任何字段</p>
                </div>

                <form v-else class="space-y-6" @submit.prevent>
                  <div
                    v-for="field in template.config.fields"
                    :key="field.id"
                    class="space-y-2"
                  >
                    <!-- 字段标签 -->
                    <label class="block text-sm font-medium text-gray-700">
                      {{ field.label }}
                      <span v-if="field.required" class="text-red-500">*</span>
                    </label>

                    <!-- 字段描述 -->
                    <p v-if="field.description" class="text-xs text-gray-500 -mt-1">
                      {{ field.description }}
                    </p>

                    <!-- 字段预览组件 -->
                    <FormFieldPreview :field="field" />

                    <!-- 验证提示 -->
                    <div v-if="field.validation" class="text-xs text-gray-500">
                      <div v-if="field.validation.minLength || field.validation.maxLength">
                        长度限制：{{ field.validation.minLength || 0 }} - {{ field.validation.maxLength || '无限制' }} 个字符
                      </div>
                      <div v-if="field.validation.min !== undefined || field.validation.max !== undefined">
                        数值范围：{{ field.validation.min || '无限制' }} - {{ field.validation.max || '无限制' }}
                      </div>
                    </div>
                  </div>

                  <!-- 提交按钮 -->
                  <div class="pt-4 border-t">
                    <button
                      type="submit"
                      class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                      disabled
                    >
                      提交表单（预览模式）
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import api from '@/utils/api';
import FormFieldPreview from '@/components/form-builder/FormFieldPreview.vue';

// 字段类型定义
interface FormField {
  id: string;
  type: string;
  label: string;
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: any;
  description?: string;
}

interface FormTemplate {
  id?: number;
  name: string;
  category: string;
  description: string;
  status: string;
  config: {
    fields: FormField[];
  };
  created_at: string;
  updated_at: string;
}

const router = useRouter();
const route = useRoute();

// 响应式数据
const template = reactive<FormTemplate>({
  name: '',
  category: '',
  description: '',
  status: 'draft',
  config: {
    fields: []
  },
  created_at: '',
  updated_at: ''
});

const loading = ref(true);
const duplicating = ref(false);

// 计算属性
const fieldStats = computed(() => {
  const fields = template.config?.fields || [];
  const stats = [
    { type: 'text', label: '单行文本', count: 0 },
    { type: 'textarea', label: '多行文本', count: 0 },
    { type: 'number', label: '数字', count: 0 },
    { type: 'select', label: '下拉选择', count: 0 },
    { type: 'checkbox', label: '多选框', count: 0 },
    { type: 'radio', label: '单选框', count: 0 },
    { type: 'date', label: '日期', count: 0 },
    { type: 'file', label: '文件上传', count: 0 }
  ];

  fields.forEach(field => {
    const stat = stats.find(s => s.type === field.type);
    if (stat) {
      stat.count++;
    }
  });

  return stats.filter(stat => stat.count > 0);
});

// 生命周期
onMounted(async () => {
  await loadTemplate();
});

// 方法
const loadTemplate = async () => {
  try {
    loading.value = true;
    const templateId = route.params.id;
    const response = await api.get(`/api/v1/form-templates/${templateId}`);
    const data = response.data.data;
    
    // 更新模板数据
    Object.assign(template, {
      id: data.id,
      name: data.name,
      category: data.category,
      description: data.description,
      status: data.status,
      config: data.config || { fields: [] },
      created_at: data.created_at,
      updated_at: data.updated_at
    });
  } catch (error) {
    console.error('加载模板失败:', error);
    alert('加载模板失败，请重试');
    router.push('/form-templates');
  } finally {
    loading.value = false;
  }
};

const duplicateTemplate = async () => {
  try {
    duplicating.value = true;
    await api.post(`/api/v1/form-templates/${template.id}/duplicate`);
    alert('模板复制成功！');
    router.push('/form-templates');
  } catch (error) {
    console.error('复制模板失败:', error);
    alert('复制模板失败，请重试');
  } finally {
    duplicating.value = false;
  }
};

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
};
</script>
