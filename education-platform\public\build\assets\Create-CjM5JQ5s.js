import{d as U,A as D,r as p,c as i,a as e,B as x,b as f,i as g,j as N,k as $,t as u,e as m,v as k,q as S,y as A,F as y,p as b,s as H,f as q,o as n,l as E}from"./app-D0Qwllno.js";import{_ as L,F as P,a as I}from"./FormPreviewModal-BS2pnpF_.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const R={class:"min-h-screen bg-gray-50"},G={class:"bg-white shadow"},J={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},K={class:"flex justify-between h-16"},O={class:"flex items-center space-x-4"},Q={class:"flex items-center space-x-4"},W=["disabled"],X={key:0,class:"animate-spin h-4 w-4",fill:"none",viewBox:"0 0 24 24"},Y={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},Z={class:"flex gap-6"},T={class:"w-1/4 space-y-6"},ee={class:"bg-white rounded-lg shadow p-6"},te={class:"space-y-4"},oe={class:"bg-white rounded-lg shadow p-6"},se={class:"space-y-2"},le=["onClick"],ne={class:"flex-shrink-0 mr-3"},ie={class:"h-5 w-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},re={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h7"},ae={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"},de={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 20l4-16m2 16l4-16M6 9h14M4 15h14"},ce={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 9l4-4 4 4m0 6l-4 4-4-4"},pe={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},ue={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},me={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"},he={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"},ve={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h7"},xe={class:"text-sm font-medium text-gray-900"},fe={class:"text-xs text-gray-500"},ge={class:"flex-1"},ke={class:"bg-white rounded-lg shadow"},ye={class:"p-6 min-h-96"},be={key:0,class:"text-center py-12"},_e={key:1,class:"space-y-4"},we={class:"w-1/4"},Me={class:"bg-white rounded-lg shadow p-6"},Ce={key:0},Ve={key:1,class:"text-center py-8"},Ue=U({__name:"Create",setup(je){const _=q(),o=D({name:"",category:"",description:"",config:{fields:[]}}),a=p(null),d=p(!1),c=p(!1),w=[{type:"text",label:"单行文本",description:"输入单行文本内容"},{type:"textarea",label:"多行文本",description:"输入多行文本内容"},{type:"number",label:"数字",description:"输入数字"},{type:"select",label:"下拉选择",description:"从选项中选择一个"},{type:"checkbox",label:"多选框",description:"选择多个选项"},{type:"radio",label:"单选框",description:"选择一个选项"},{type:"date",label:"日期",description:"选择日期"},{type:"file",label:"文件上传",description:"上传文件"}],M=s=>{const t={id:`field_${Date.now()}`,type:s.type,label:s.label,required:!1,placeholder:`请输入${s.label}`};["select","checkbox","radio"].includes(s.type)&&(t.options=["选项1","选项2","选项3"]),o.config.fields.push(t),a.value=t},h=(s,t)=>{var r;o.config.fields[s]=t,((r=a.value)==null?void 0:r.id)===t.id&&(a.value=t)},C=s=>{var r;const t=o.config.fields[s];o.config.fields.splice(s,1),((r=a.value)==null?void 0:r.id)===t.id&&(a.value=null)},V=s=>{if(s>0){const t=o.config.fields.splice(s,1)[0];o.config.fields.splice(s-1,0,t)}},j=s=>{if(s<o.config.fields.length-1){const t=o.config.fields.splice(s,1)[0];o.config.fields.splice(s+1,0,t)}},F=s=>{const t=o.config.fields.findIndex(r=>r.id===s.id);t!==-1&&h(t,s)},B=async()=>{if(!o.name||!o.category){alert("请填写模板名称和分类");return}if(o.config.fields.length===0){alert("请至少添加一个字段");return}try{d.value=!0,await H.post("/api/v1/form-templates",o),alert("模板创建成功！"),_.push("/form-templates")}catch(s){console.error("保存模板失败:",s),alert("保存模板失败，请重试")}finally{d.value=!1}},z=()=>{c.value=!0};return(s,t)=>{const r=$("router-link");return n(),i("div",R,[e("nav",G,[e("div",J,[e("div",K,[e("div",O,[g(r,{to:"/form-templates",class:"text-indigo-600 hover:text-indigo-500"},{default:N(()=>t[4]||(t[4]=[E(" ← 返回模板列表 ",-1)])),_:1,__:[4]}),t[5]||(t[5]=e("h1",{class:"text-xl font-semibold text-gray-900"}," 创建表单模板 ",-1))]),e("div",Q,[e("button",{onClick:B,disabled:d.value,class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"},[d.value?(n(),i("svg",X,t[6]||(t[6]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):f("",!0),e("span",null,u(d.value?"保存中...":"保存模板"),1)],8,W),e("button",{onClick:z,class:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"},t[7]||(t[7]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1),e("span",null,"预览",-1)]))])])])]),e("div",Y,[e("div",Z,[e("div",T,[e("div",ee,[t[12]||(t[12]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"基本信息",-1)),e("div",te,[e("div",null,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模板名称",-1)),m(e("input",{"onUpdate:modelValue":t[0]||(t[0]=l=>o.name=l),type:"text",placeholder:"请输入模板名称",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[k,o.name]])]),e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"分类",-1)),m(e("select",{"onUpdate:modelValue":t[1]||(t[1]=l=>o.category=l),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[9]||(t[9]=[A('<option value="">请选择分类</option><option value="基础信息">基础信息</option><option value="教学统计">教学统计</option><option value="财务统计">财务统计</option><option value="人员统计">人员统计</option><option value="设施统计">设施统计</option>',6)]),512),[[S,o.category]])]),e("div",null,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"描述",-1)),m(e("textarea",{"onUpdate:modelValue":t[2]||(t[2]=l=>o.description=l),rows:"3",placeholder:"请输入模板描述",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[k,o.description]])])])]),e("div",oe,[t[13]||(t[13]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"字段组件",-1)),e("div",se,[(n(),i(y,null,b(w,l=>e("div",{key:l.type,onClick:v=>M(l),class:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"},[e("div",ne,[(n(),i("svg",ie,[l.type==="text"?(n(),i("path",re)):l.type==="textarea"?(n(),i("path",ae)):l.type==="number"?(n(),i("path",de)):l.type==="select"?(n(),i("path",ce)):l.type==="checkbox"?(n(),i("path",pe)):l.type==="radio"?(n(),i("path",ue)):l.type==="date"?(n(),i("path",me)):l.type==="file"?(n(),i("path",he)):(n(),i("path",ve))]))]),e("div",null,[e("div",xe,u(l.label),1),e("div",fe,u(l.description),1)])],8,le)),64))])])]),e("div",ge,[e("div",ke,[t[15]||(t[15]=e("div",{class:"p-6 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"表单设计"),e("p",{class:"text-sm text-gray-500 mt-1"},"拖拽左侧组件到此区域，或点击组件直接添加")],-1)),e("div",ye,[o.config.fields.length===0?(n(),i("div",be,t[14]||(t[14]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"暂无字段",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"从左侧选择字段组件开始设计表单",-1)]))):(n(),i("div",_e,[(n(!0),i(y,null,b(o.config.fields,(l,v)=>(n(),x(I,{key:l.id,field:l,index:v,onUpdate:h,onDelete:C,onMoveUp:V,onMoveDown:j},null,8,["field","index"]))),128))]))])])]),e("div",we,[e("div",Me,[t[17]||(t[17]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"字段属性",-1)),a.value?(n(),i("div",Ce,[g(L,{field:a.value,onUpdate:F},null,8,["field"])])):(n(),i("div",Ve,t[16]||(t[16]=[e("svg",{class:"mx-auto h-8 w-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})],-1),e("p",{class:"mt-2 text-sm text-gray-500"},"选择字段以编辑属性",-1)])))])])])]),c.value?(n(),x(P,{key:0,template:o,onClose:t[3]||(t[3]=l=>c.value=!1)},null,8,["template"])):f("",!0)])}}});export{Ue as default};
