<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建权限
        $permissions = [
            // 组织架构管理
            'organizations.view',
            'organizations.create',
            'organizations.edit',
            'organizations.delete',

            // 用户管理
            'users.view',
            'users.create',
            'users.edit',
            'users.delete',

            // 表单模板管理
            'form-templates.view',
            'form-templates.create',
            'form-templates.edit',
            'form-templates.delete',
            'form-templates.publish',

            // 统计任务管理
            'statistics-tasks.view',
            'statistics-tasks.create',
            'statistics-tasks.edit',
            'statistics-tasks.delete',
            'statistics-tasks.publish',

            // 数据提交管理
            'data-submissions.view',
            'data-submissions.create',
            'data-submissions.edit',
            'data-submissions.delete',
            'data-submissions.submit',
            'data-submissions.review',

            // 角色权限管理
            'roles.view',
            'roles.create',
            'roles.edit',
            'roles.delete',

            // 系统管理
            'system.settings',
            'system.logs',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // 创建角色
        $superAdmin = Role::firstOrCreate(['name' => '超级管理员'], ['display_name' => '超级管理员']);
        $admin = Role::firstOrCreate(['name' => '系统管理员'], ['display_name' => '系统管理员']);
        $deptManager = Role::firstOrCreate(['name' => '股室管理员'], ['display_name' => '股室管理员']);
        $districtManager = Role::firstOrCreate(['name' => '学区管理员'], ['display_name' => '学区管理员']);
        $schoolAdmin = Role::firstOrCreate(['name' => '学校管理员'], ['display_name' => '学校管理员']);
        $teacher = Role::firstOrCreate(['name' => '填报员'], ['display_name' => '填报员']);

        // 分配权限
        $superAdmin->givePermissionTo(Permission::all());

        $admin->givePermissionTo([
            'organizations.view', 'organizations.create', 'organizations.edit',
            'users.view', 'users.create', 'users.edit',
            'form-templates.view', 'form-templates.create', 'form-templates.edit', 'form-templates.publish',
            'statistics-tasks.view', 'statistics-tasks.create', 'statistics-tasks.edit', 'statistics-tasks.publish',
            'data-submissions.view', 'data-submissions.review',
            'roles.view', 'roles.create', 'roles.edit',
        ]);

        $deptManager->givePermissionTo([
            'organizations.view',
            'users.view',
            'form-templates.view', 'form-templates.create', 'form-templates.edit', 'form-templates.publish',
            'statistics-tasks.view', 'statistics-tasks.create', 'statistics-tasks.edit', 'statistics-tasks.publish',
            'data-submissions.view', 'data-submissions.review',
        ]);

        $districtManager->givePermissionTo([
            'organizations.view',
            'users.view',
            'form-templates.view',
            'statistics-tasks.view',
            'data-submissions.view', 'data-submissions.review',
        ]);

        $schoolAdmin->givePermissionTo([
            'organizations.view',
            'users.view', 'users.create', 'users.edit',
            'form-templates.view',
            'statistics-tasks.view',
            'data-submissions.view', 'data-submissions.create', 'data-submissions.edit', 'data-submissions.submit',
        ]);

        $teacher->givePermissionTo([
            'form-templates.view',
            'statistics-tasks.view',
            'data-submissions.view', 'data-submissions.create', 'data-submissions.edit', 'data-submissions.submit',
        ]);
    }
}
