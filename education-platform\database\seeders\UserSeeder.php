<?php

namespace Database\Seeders;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取组织
        $county = Organization::where('type', 'county')->first();
        $district1 = Organization::where('code', 'EDU_DISTRICT_001')->first();
        $district2 = Organization::where('code', 'EDU_DISTRICT_002')->first();
        $school1 = Organization::where('code', 'SCHOOL_001')->first();
        $school2 = Organization::where('code', 'SCHOOL_002')->first();

        // 创建超级管理员
        $superAdmin = User::create([
            'name' => '超级管理员',
            'email' => '<EMAIL>',
            'phone' => '13800000000',
            'password' => Hash::make('password'),
            'organization_id' => $county->id,
            'status' => 1
        ]);
        $superAdmin->assignRole('超级管理员');

        // 创建区县教育局管理员
        $countyAdmin = User::create([
            'name' => '教育局管理员',
            'email' => '<EMAIL>',
            'phone' => '13800000001',
            'password' => Hash::make('password'),
            'organization_id' => $county->id,
            'status' => 1
        ]);
        $countyAdmin->assignRole('系统管理员');

        // 创建股室管理员
        $deptManager = User::create([
            'name' => '基教股管理员',
            'email' => '<EMAIL>',
            'phone' => '13800000002',
            'password' => Hash::make('password'),
            'organization_id' => $county->id,
            'status' => 1
        ]);
        $deptManager->assignRole('股室管理员');

        // 创建学区管理员
        $districtManager1 = User::create([
            'name' => '第一学区管理员',
            'email' => '<EMAIL>',
            'phone' => '13800000003',
            'password' => Hash::make('password'),
            'organization_id' => $district1->id,
            'status' => 1
        ]);
        $districtManager1->assignRole('学区管理员');

        $districtManager2 = User::create([
            'name' => '第二学区管理员',
            'email' => '<EMAIL>',
            'phone' => '13800000004',
            'password' => Hash::make('password'),
            'organization_id' => $district2->id,
            'status' => 1
        ]);
        $districtManager2->assignRole('学区管理员');

        // 创建学校管理员
        $schoolAdmin1 = User::create([
            'name' => '示例小学校长',
            'email' => '<EMAIL>',
            'phone' => '13800000005',
            'password' => Hash::make('password'),
            'organization_id' => $school1->id,
            'status' => 1
        ]);
        $schoolAdmin1->assignRole('学校管理员');

        $schoolAdmin2 = User::create([
            'name' => '示例中学校长',
            'email' => '<EMAIL>',
            'phone' => '13800000006',
            'password' => Hash::make('password'),
            'organization_id' => $school2->id,
            'status' => 1
        ]);
        $schoolAdmin2->assignRole('学校管理员');

        // 创建填报员
        $teacher1 = User::create([
            'name' => '示例小学填报员',
            'email' => '<EMAIL>',
            'phone' => '13800000007',
            'password' => Hash::make('password'),
            'organization_id' => $school1->id,
            'status' => 1
        ]);
        $teacher1->assignRole('填报员');

        $teacher2 = User::create([
            'name' => '示例中学填报员',
            'email' => '<EMAIL>',
            'phone' => '13800000008',
            'password' => Hash::make('password'),
            'organization_id' => $school2->id,
            'status' => 1
        ]);
        $teacher2->assignRole('填报员');
    }
}
