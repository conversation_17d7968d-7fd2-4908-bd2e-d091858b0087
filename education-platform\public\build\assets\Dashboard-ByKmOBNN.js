import{d as u,u as g,r as f,g as x,c as h,a as s,t as o,h as v,i as n,j as d,k as _,f as p,o as w,l as r}from"./app-D0Qwllno.js";const b={class:"min-h-screen bg-gray-50"},k={class:"bg-white shadow"},y={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},M={class:"flex justify-between h-16"},j={class:"flex items-center space-x-4"},H={class:"text-sm text-gray-700"},C={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},z={class:"px-4 py-6 sm:px-0"},B={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},T={class:"bg-white overflow-hidden shadow rounded-lg"},V={class:"p-5"},N={class:"flex items-center"},S={class:"ml-5 w-0 flex-1"},D={class:"text-lg font-medium text-gray-900"},A={class:"bg-white overflow-hidden shadow rounded-lg"},E={class:"p-5"},L={class:"flex items-center"},R={class:"ml-5 w-0 flex-1"},q={class:"text-lg font-medium text-gray-900"},F={class:"bg-white overflow-hidden shadow rounded-lg"},G={class:"p-5"},I={class:"flex items-center"},J={class:"ml-5 w-0 flex-1"},K={class:"text-lg font-medium text-gray-900"},O={class:"bg-white overflow-hidden shadow rounded-lg"},P={class:"p-5"},Q={class:"flex items-center"},U={class:"ml-5 w-0 flex-1"},W={class:"text-lg font-medium text-gray-900"},X={class:"bg-white shadow rounded-lg"},Y={class:"px-4 py-5 sm:p-6"},Z={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4"},es=u({__name:"Dashboard",setup($){const c=p(),l=g(),e=f({formTemplates:0,statisticsTasks:0,users:0,organizations:0}),m=async()=>{await l.logout(),c.push("/login")};return x(async()=>{e.value={formTemplates:12,statisticsTasks:8,users:156,organizations:45}}),(ss,t)=>{var a;const i=_("router-link");return w(),h("div",b,[s("nav",k,[s("div",y,[s("div",M,[t[0]||(t[0]=s("div",{class:"flex items-center"},[s("h1",{class:"text-xl font-semibold text-gray-900"}," 教育数据统计管理平台 ")],-1)),s("div",j,[s("span",H," 欢迎，"+o((a=v(l).user)==null?void 0:a.name),1),s("button",{onClick:m,class:"text-sm text-gray-500 hover:text-gray-700"}," 退出登录 ")])])])]),s("div",C,[s("div",z,[s("div",B,[s("div",T,[s("div",V,[s("div",N,[t[2]||(t[2]=s("div",{class:"flex-shrink-0"},[s("div",{class:"w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center"},[s("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 10h10M7 13h10"})])])],-1)),s("div",S,[s("dl",null,[t[1]||(t[1]=s("dt",{class:"text-sm font-medium text-gray-500 truncate"}," 表单模板 ",-1)),s("dd",D,o(e.value.formTemplates),1)])])])])]),s("div",A,[s("div",E,[s("div",L,[t[4]||(t[4]=s("div",{class:"flex-shrink-0"},[s("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[s("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])])],-1)),s("div",R,[s("dl",null,[t[3]||(t[3]=s("dt",{class:"text-sm font-medium text-gray-500 truncate"}," 统计任务 ",-1)),s("dd",q,o(e.value.statisticsTasks),1)])])])])]),s("div",F,[s("div",G,[s("div",I,[t[6]||(t[6]=s("div",{class:"flex-shrink-0"},[s("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[s("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])])],-1)),s("div",J,[s("dl",null,[t[5]||(t[5]=s("dt",{class:"text-sm font-medium text-gray-500 truncate"}," 用户数量 ",-1)),s("dd",K,o(e.value.users),1)])])])])]),s("div",O,[s("div",P,[s("div",Q,[t[8]||(t[8]=s("div",{class:"flex-shrink-0"},[s("div",{class:"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center"},[s("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 10h10M7 13h10"})])])],-1)),s("div",U,[s("dl",null,[t[7]||(t[7]=s("dt",{class:"text-sm font-medium text-gray-500 truncate"}," 组织机构 ",-1)),s("dd",W,o(e.value.organizations),1)])])])])])]),s("div",X,[s("div",Y,[t[13]||(t[13]=s("h3",{class:"text-lg leading-6 font-medium text-gray-900 mb-4"}," 快捷操作 ",-1)),s("div",Z,[n(i,{to:"/organizations",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},{default:d(()=>t[9]||(t[9]=[r(" 组织管理 ",-1)])),_:1,__:[9]}),n(i,{to:"/users",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},{default:d(()=>t[10]||(t[10]=[r(" 用户管理 ",-1)])),_:1,__:[10]}),n(i,{to:"/form-templates",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},{default:d(()=>t[11]||(t[11]=[r(" 表单模板 ",-1)])),_:1,__:[11]}),n(i,{to:"/statistics-tasks",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},{default:d(()=>t[12]||(t[12]=[r(" 统计任务 ",-1)])),_:1,__:[12]})])])])])])])}}});export{es as default};
