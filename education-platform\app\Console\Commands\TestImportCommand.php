<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Api\OrganizationController;
use App\Http\Controllers\Api\UserController;

class TestImportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试批量导入功能';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('测试批量导入功能...');

        // 检查控制器方法是否存在
        $this->info('1. 检查控制器方法...');

        if (method_exists(OrganizationController::class, 'import')) {
            $this->info('   ✓ OrganizationController::import 方法存在');
        } else {
            $this->error('   ✗ OrganizationController::import 方法不存在');
        }

        if (method_exists(UserController::class, 'import')) {
            $this->info('   ✓ UserController::import 方法存在');
        } else {
            $this->error('   ✗ UserController::import 方法不存在');
        }

        // 检查测试文件是否存在
        $this->info('2. 检查测试文件...');

        $orgFile = storage_path('app/test_organizations.csv');
        $userFile = storage_path('app/test_users.csv');

        if (file_exists($orgFile)) {
            $this->info('   ✓ 组织测试文件存在: ' . $orgFile);
        } else {
            $this->error('   ✗ 组织测试文件不存在: ' . $orgFile);
        }

        if (file_exists($userFile)) {
            $this->info('   ✓ 用户测试文件存在: ' . $userFile);
        } else {
            $this->error('   ✗ 用户测试文件不存在: ' . $userFile);
        }

        $this->info('测试完成！');

        return 0;
    }
}
