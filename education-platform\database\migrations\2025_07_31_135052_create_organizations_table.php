<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organizations', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('组织名称');
            $table->string('code', 50)->unique()->comment('组织代码');
            $table->enum('type', ['county', 'district', 'school', 'kindergarten'])->comment('组织类型');
            $table->unsignedBigInteger('parent_id')->nullable()->comment('父级组织ID');
            $table->tinyInteger('level')->comment('组织层级');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->json('contact_info')->nullable()->comment('联系信息');
            $table->tinyInteger('status')->default(1)->comment('状态：1启用，0禁用');
            $table->timestamps();

            // 索引
            $table->index('parent_id');
            $table->index(['type', 'level']);
            $table->index('status');

            // 外键约束
            $table->foreign('parent_id')->references('id')->on('organizations')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organizations');
    }
};
