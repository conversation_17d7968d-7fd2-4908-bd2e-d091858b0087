<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organization;

class ShowOrganizations extends Command
{
    protected $signature = 'show:organizations';
    protected $description = '显示所有组织信息';

    public function handle()
    {
        $organizations = Organization::orderBy('level')->orderBy('id')->get();

        $this->info('当前组织结构:');
        $this->info('ID | 名称 | 类型 | 层级 | 父级ID | 父级名称');
        $this->info('---|------|------|------|--------|----------');

        foreach ($organizations as $org) {
            $parentName = $org->parent ? $org->parent->name : '无';
            $this->info(sprintf(
                '%d | %s | %s | %d | %s | %s',
                $org->id,
                $org->name,
                $org->type,
                $org->level,
                $org->parent_id ?: '无',
                $parentName
            ));
        }

        return 0;
    }
}
