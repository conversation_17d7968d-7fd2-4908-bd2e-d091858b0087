<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class FormTemplate extends Model
{
    protected $fillable = [
        'name',
        'category',
        'description',
        'config',
        'validation_rules',
        'conditional_logic',
        'version',
        'status',
        'creator_id',
        'organization_id',
        'usage_count'
    ];

    protected $casts = [
        'config' => 'array',
        'validation_rules' => 'array',
        'conditional_logic' => 'array'
    ];

    /**
     * 创建者
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * 所属组织
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * 基于此模板的统计任务
     */
    public function statisticsTasks(): HasMany
    {
        return $this->hasMany(StatisticsTask::class);
    }

    /**
     * 增加使用次数
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    /**
     * 检查是否可以编辑
     */
    public function canEdit(): bool
    {
        return $this->status === 'draft' || $this->statisticsTasks()->where('status', '!=', 'completed')->doesntExist();
    }

    /**
     * 发布模板
     */
    public function publish(): bool
    {
        if ($this->status === 'draft') {
            return $this->update(['status' => 'published']);
        }

        return false;
    }

    /**
     * 归档模板
     */
    public function archive(): bool
    {
        if ($this->canEdit()) {
            return $this->update(['status' => 'archived']);
        }

        return false;
    }
}
