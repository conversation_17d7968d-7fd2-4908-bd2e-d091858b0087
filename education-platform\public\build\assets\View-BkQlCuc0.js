import{d as M,c as l,o,a as e,F as v,p as x,t as a,b as c,A as B,r as q,m as j,g as F,C as H,s as C,f as N,y as S,i as w,j as L,k as A,x as D,w as T,l as z}from"./app-D0Qwllno.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const I={class:"form-field-preview"},P=["placeholder","required","minlength","maxlength"],R=["placeholder","rows","required","minlength","maxlength"],O=["placeholder","required","min","max","step"],G=["required"],J=["value"],K={key:4,class:"space-y-2"},Q=["id","value"],U=["for"],W={key:5,class:"space-y-2"},X=["name","id","value"],Y=["for"],Z=["required"],ee={key:7},te={class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors"},se={class:"mt-4"},le={class:"cursor-pointer"},oe=["required","accept","multiple"],ie={key:0,class:"mt-2 text-xs text-gray-500"},ae={key:0},re={key:1},de={key:2},ne={key:8,class:"p-4 bg-gray-100 border border-gray-300 rounded-md text-center"},ue={class:"text-sm text-gray-500"},ce=M({__name:"FormFieldPreview",props:{field:{}},setup(V){return(t,u)=>{var d,m,p,b,h,_,y,n,s;return o(),l("div",I,[t.field.type==="text"?(o(),l("input",{key:0,type:"text",placeholder:t.field.placeholder,required:t.field.required,minlength:(d=t.field.validation)==null?void 0:d.minLength,maxlength:(m=t.field.validation)==null?void 0:m.maxLength,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:""},null,8,P)):t.field.type==="textarea"?(o(),l("textarea",{key:1,placeholder:t.field.placeholder,rows:t.field.rows||3,required:t.field.required,minlength:(p=t.field.validation)==null?void 0:p.minLength,maxlength:(b=t.field.validation)==null?void 0:b.maxLength,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:""},null,8,R)):t.field.type==="number"?(o(),l("input",{key:2,type:"number",placeholder:t.field.placeholder,required:t.field.required,min:(h=t.field.validation)==null?void 0:h.min,max:(_=t.field.validation)==null?void 0:_.max,step:(y=t.field.validation)!=null&&y.precision?Math.pow(10,-t.field.validation.precision):1,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:""},null,8,O)):t.field.type==="select"?(o(),l("select",{key:3,required:t.field.required,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:""},[u[0]||(u[0]=e("option",{value:""},"请选择",-1)),(o(!0),l(v,null,x(t.field.options,i=>(o(),l("option",{key:i,value:i},a(i),9,J))),128))],8,G)):t.field.type==="checkbox"?(o(),l("div",K,[(o(!0),l(v,null,x(t.field.options,i=>(o(),l("div",{key:i,class:"flex items-center"},[e("input",{type:"checkbox",id:`preview_${t.field.id}_${i}`,value:i,class:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",disabled:""},null,8,Q),e("label",{for:`preview_${t.field.id}_${i}`,class:"ml-2 text-sm text-gray-700"},a(i),9,U)]))),128))])):t.field.type==="radio"?(o(),l("div",W,[(o(!0),l(v,null,x(t.field.options,i=>(o(),l("div",{key:i,class:"flex items-center"},[e("input",{type:"radio",name:`preview_${t.field.id}`,id:`preview_${t.field.id}_${i}`,value:i,class:"h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500",disabled:""},null,8,X),e("label",{for:`preview_${t.field.id}_${i}`,class:"ml-2 text-sm text-gray-700"},a(i),9,Y)]))),128))])):t.field.type==="date"?(o(),l("input",{key:6,type:"date",required:t.field.required,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:""},null,8,Z)):t.field.type==="file"?(o(),l("div",ee,[e("div",te,[u[3]||(u[3]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1)),e("div",se,[e("label",le,[u[1]||(u[1]=e("span",{class:"text-sm text-blue-600 hover:text-blue-500"},"点击上传文件",-1)),e("input",{type:"file",class:"hidden",required:t.field.required,accept:(n=t.field.validation)==null?void 0:n.accept,multiple:(s=t.field.validation)==null?void 0:s.multiple,disabled:""},null,8,oe)]),u[2]||(u[2]=e("p",{class:"text-xs text-gray-500 mt-1"},"或拖拽文件到此处",-1))]),t.field.validation?(o(),l("div",ie,[t.field.validation.accept?(o(),l("div",ae," 支持格式："+a(t.field.validation.accept),1)):c("",!0),t.field.validation.maxSize?(o(),l("div",re," 最大大小："+a(t.field.validation.maxSize)+"MB ",1)):c("",!0),t.field.validation.multiple?(o(),l("div",de," 支持多文件上传 ")):c("",!0)])):c("",!0)])])):(o(),l("div",ne,[e("p",ue,"未知字段类型: "+a(t.field.type),1)]))])}}}),pe=E(ce,[["__scopeId","data-v-2625611e"]]),me={class:"min-h-screen bg-gray-50"},fe={key:0,class:"flex items-center justify-center min-h-screen"},ye={key:1},ge={class:"bg-white shadow"},ve={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},xe={class:"flex justify-between h-16"},be={class:"flex items-center space-x-4"},he={class:"text-xl font-semibold text-gray-900"},_e={class:"flex items-center space-x-4"},ke=["disabled"],we={key:0,class:"animate-spin h-4 w-4",fill:"none",viewBox:"0 0 24 24"},$e={key:1,class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},qe={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},Ce={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Le={class:"lg:col-span-1"},ze={class:"bg-white rounded-lg shadow p-6"},Me={class:"space-y-4"},Ve={class:"mt-1 text-sm text-gray-900"},Be={class:"mt-1 text-sm text-gray-900"},je={class:"mt-1 text-sm text-gray-900"},Fe={class:"mt-1 text-sm text-gray-900"},He={class:"mt-1 text-sm text-gray-900"},Ne={class:"mt-1 text-sm text-gray-900"},Se={class:"mt-6 bg-white rounded-lg shadow p-6"},Ae={class:"space-y-3"},De={class:"text-sm text-gray-600"},Te={class:"text-sm font-medium text-gray-900"},Ee={class:"lg:col-span-2"},Ie={class:"bg-white rounded-lg shadow"},Pe={class:"p-6"},Re={key:0,class:"text-center py-12"},Oe={class:"block text-sm font-medium text-gray-700"},Ge={key:0,class:"text-red-500"},Je={key:0,class:"text-xs text-gray-500 -mt-1"},Ke={key:1,class:"text-xs text-gray-500"},Qe={key:0},Ue={key:1},Ye=M({__name:"View",setup(V){const t=N(),u=H(),d=B({name:"",category:"",description:"",status:"draft",config:{fields:[]},created_at:"",updated_at:""}),m=q(!0),p=q(!1),b=j(()=>{var i;const n=((i=d.config)==null?void 0:i.fields)||[],s=[{type:"text",label:"单行文本",count:0},{type:"textarea",label:"多行文本",count:0},{type:"number",label:"数字",count:0},{type:"select",label:"下拉选择",count:0},{type:"checkbox",label:"多选框",count:0},{type:"radio",label:"单选框",count:0},{type:"date",label:"日期",count:0},{type:"file",label:"文件上传",count:0}];return n.forEach(f=>{const g=s.find(k=>k.type===f.type);g&&g.count++}),s.filter(f=>f.count>0)});F(async()=>{await h()});const h=async()=>{try{m.value=!0;const n=u.params.id,i=(await C.get(`/api/v1/form-templates/${n}`)).data.data;Object.assign(d,{id:i.id,name:i.name,category:i.category,description:i.description,status:i.status,config:i.config||{fields:[]},created_at:i.created_at,updated_at:i.updated_at})}catch(n){console.error("加载模板失败:",n),alert("加载模板失败，请重试"),t.push("/form-templates")}finally{m.value=!1}},_=async()=>{try{p.value=!0,await C.post(`/api/v1/form-templates/${d.id}/duplicate`),alert("模板复制成功！"),t.push("/form-templates")}catch(n){console.error("复制模板失败:",n),alert("复制模板失败，请重试")}finally{p.value=!1}},y=n=>n?new Date(n).toLocaleString("zh-CN"):"";return(n,s)=>{var f,g,k,$;const i=A("router-link");return o(),l("div",me,[m.value?(o(),l("div",fe,s[1]||(s[1]=[S('<div class="text-center"><svg class="animate-spin h-8 w-8 text-blue-600 mx-auto" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p class="mt-2 text-sm text-gray-600">加载中...</p></div>',1)]))):(o(),l("div",ye,[e("nav",ge,[e("div",ve,[e("div",xe,[e("div",be,[w(i,{to:"/form-templates",class:"text-indigo-600 hover:text-indigo-500"},{default:L(()=>s[2]||(s[2]=[z(" ← 返回模板列表 ",-1)])),_:1,__:[2]}),e("h1",he," 查看表单模板 - "+a(d.name),1)]),e("div",_e,[w(i,{to:`/form-templates/${d.id}/edit`,class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"},{default:L(()=>s[3]||(s[3]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),e("span",null,"编辑模板",-1)])),_:1,__:[3]},8,["to"]),e("button",{onClick:_,disabled:p.value,class:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2"},[p.value?(o(),l("svg",we,s[4]||(s[4]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),l("svg",$e,s[5]||(s[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"},null,-1)]))),e("span",null,a(p.value?"复制中...":"复制模板"),1)],8,ke)])])])]),e("div",qe,[e("div",Ce,[e("div",Le,[e("div",ze,[s[13]||(s[13]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"模板信息",-1)),e("div",Me,[e("div",null,[s[6]||(s[6]=e("label",{class:"block text-sm font-medium text-gray-700"},"模板名称",-1)),e("p",Ve,a(d.name),1)]),e("div",null,[s[7]||(s[7]=e("label",{class:"block text-sm font-medium text-gray-700"},"分类",-1)),e("p",Be,a(d.category),1)]),e("div",null,[s[8]||(s[8]=e("label",{class:"block text-sm font-medium text-gray-700"},"描述",-1)),e("p",je,a(d.description||"无描述"),1)]),e("div",null,[s[9]||(s[9]=e("label",{class:"block text-sm font-medium text-gray-700"},"状态",-1)),e("span",{class:D(["mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",d.status==="published"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"])},a(d.status==="published"?"已发布":"草稿"),3)]),e("div",null,[s[10]||(s[10]=e("label",{class:"block text-sm font-medium text-gray-700"},"字段数量",-1)),e("p",Fe,a(((g=(f=d.config)==null?void 0:f.fields)==null?void 0:g.length)||0)+" 个字段",1)]),e("div",null,[s[11]||(s[11]=e("label",{class:"block text-sm font-medium text-gray-700"},"创建时间",-1)),e("p",He,a(y(d.created_at)),1)]),e("div",null,[s[12]||(s[12]=e("label",{class:"block text-sm font-medium text-gray-700"},"更新时间",-1)),e("p",Ne,a(y(d.updated_at)),1)])])]),e("div",Se,[s[14]||(s[14]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"字段统计",-1)),e("div",Ae,[(o(!0),l(v,null,x(b.value,r=>(o(),l("div",{key:r.type,class:"flex justify-between items-center"},[e("span",De,a(r.label),1),e("span",Te,a(r.count),1)]))),128))])])]),e("div",Ee,[e("div",Ie,[s[17]||(s[17]=e("div",{class:"p-6 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"表单预览"),e("p",{class:"text-sm text-gray-500 mt-1"},"以下是表单的实际显示效果")],-1)),e("div",Pe,[($=(k=d.config)==null?void 0:k.fields)!=null&&$.length?(o(),l("form",{key:1,class:"space-y-6",onSubmit:s[0]||(s[0]=T(()=>{},["prevent"]))},[(o(!0),l(v,null,x(d.config.fields,r=>(o(),l("div",{key:r.id,class:"space-y-2"},[e("label",Oe,[z(a(r.label)+" ",1),r.required?(o(),l("span",Ge,"*")):c("",!0)]),r.description?(o(),l("p",Je,a(r.description),1)):c("",!0),w(pe,{field:r},null,8,["field"]),r.validation?(o(),l("div",Ke,[r.validation.minLength||r.validation.maxLength?(o(),l("div",Qe," 长度限制："+a(r.validation.minLength||0)+" - "+a(r.validation.maxLength||"无限制")+" 个字符 ",1)):c("",!0),r.validation.min!==void 0||r.validation.max!==void 0?(o(),l("div",Ue," 数值范围："+a(r.validation.min||"无限制")+" - "+a(r.validation.max||"无限制"),1)):c("",!0)])):c("",!0)]))),128)),s[16]||(s[16]=e("div",{class:"pt-4 border-t"},[e("button",{type:"submit",class:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors",disabled:""}," 提交表单（预览模式） ")],-1))],32)):(o(),l("div",Re,s[15]||(s[15]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"暂无字段",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"该模板还没有配置任何字段",-1)])))])])])])])]))])}}});export{Ye as default};
