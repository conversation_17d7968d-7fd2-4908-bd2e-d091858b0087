<template>
  <div class="form-field-preview">
    <!-- 单行文本 -->
    <input
      v-if="field.type === 'text'"
      type="text"
      :placeholder="field.placeholder"
      :required="field.required"
      :minlength="field.validation?.minLength"
      :maxlength="field.validation?.maxLength"
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      disabled
    />

    <!-- 多行文本 -->
    <textarea
      v-else-if="field.type === 'textarea'"
      :placeholder="field.placeholder"
      :rows="field.rows || 3"
      :required="field.required"
      :minlength="field.validation?.minLength"
      :maxlength="field.validation?.maxLength"
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      disabled
    ></textarea>

    <!-- 数字输入 -->
    <input
      v-else-if="field.type === 'number'"
      type="number"
      :placeholder="field.placeholder"
      :required="field.required"
      :min="field.validation?.min"
      :max="field.validation?.max"
      :step="field.validation?.precision ? Math.pow(10, -field.validation.precision) : 1"
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      disabled
    />

    <!-- 下拉选择 -->
    <select
      v-else-if="field.type === 'select'"
      :required="field.required"
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      disabled
    >
      <option value="">请选择</option>
      <option v-for="option in field.options" :key="option" :value="option">
        {{ option }}
      </option>
    </select>

    <!-- 多选框 -->
    <div v-else-if="field.type === 'checkbox'" class="space-y-2">
      <div v-for="option in field.options" :key="option" class="flex items-center">
        <input
          type="checkbox"
          :id="`preview_${field.id}_${option}`"
          :value="option"
          class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          disabled
        />
        <label :for="`preview_${field.id}_${option}`" class="ml-2 text-sm text-gray-700">
          {{ option }}
        </label>
      </div>
    </div>

    <!-- 单选框 -->
    <div v-else-if="field.type === 'radio'" class="space-y-2">
      <div v-for="option in field.options" :key="option" class="flex items-center">
        <input
          type="radio"
          :name="`preview_${field.id}`"
          :id="`preview_${field.id}_${option}`"
          :value="option"
          class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
          disabled
        />
        <label :for="`preview_${field.id}_${option}`" class="ml-2 text-sm text-gray-700">
          {{ option }}
        </label>
      </div>
    </div>

    <!-- 日期选择 -->
    <input
      v-else-if="field.type === 'date'"
      type="date"
      :required="field.required"
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      disabled
    />

    <!-- 文件上传 -->
    <div v-else-if="field.type === 'file'">
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        <div class="mt-4">
          <label class="cursor-pointer">
            <span class="text-sm text-blue-600 hover:text-blue-500">点击上传文件</span>
            <input
              type="file"
              class="hidden"
              :required="field.required"
              :accept="field.validation?.accept"
              :multiple="field.validation?.multiple"
              disabled
            />
          </label>
          <p class="text-xs text-gray-500 mt-1">或拖拽文件到此处</p>
        </div>
        
        <!-- 文件限制说明 -->
        <div v-if="field.validation" class="mt-2 text-xs text-gray-500">
          <div v-if="field.validation.accept">
            支持格式：{{ field.validation.accept }}
          </div>
          <div v-if="field.validation.maxSize">
            最大大小：{{ field.validation.maxSize }}MB
          </div>
          <div v-if="field.validation.multiple">
            支持多文件上传
          </div>
        </div>
      </div>
    </div>

    <!-- 未知字段类型 -->
    <div v-else class="p-4 bg-gray-100 border border-gray-300 rounded-md text-center">
      <p class="text-sm text-gray-500">未知字段类型: {{ field.type }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface FormField {
  id: string;
  type: string;
  label: string;
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: any;
  description?: string;
  rows?: number;
}

interface Props {
  field: FormField;
}

defineProps<Props>();
</script>

<style scoped>
.form-field-preview {
  /* 预览模式下的样式 */
}

.form-field-preview input:disabled,
.form-field-preview textarea:disabled,
.form-field-preview select:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
}

.form-field-preview input[type="checkbox"]:disabled,
.form-field-preview input[type="radio"]:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
}
</style>
