import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import api from '@/utils/api';

export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  organization_id?: number;
  organization?: {
    id: number;
    name: string;
    type: string;
    level: number;
  };
  roles: Array<{
    id: number;
    name: string;
    display_name: string;
  }>;
  permissions: string[];
  status: boolean;
  last_login_at?: string;
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null);
  const token = ref<string | null>(localStorage.getItem('token'));
  const loading = ref(false);

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value);
  const userRoles = computed(() => user.value?.roles.map(role => role.name) || []);
  const userPermissions = computed(() => user.value?.permissions || []);

  // 方法
  const login = async (credentials: { email: string; password: string }) => {
    loading.value = true;
    try {
      const response = await api.post('/login', credentials);
      const { token: authToken, user: userData } = response.data.data;

      token.value = authToken;
      user.value = userData;

      localStorage.setItem('token', authToken);
      api.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;

      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '登录失败'
      };
    } finally {
      loading.value = false;
    }
  };

  const logout = async () => {
    try {
      await api.post('/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      token.value = null;
      user.value = null;
      localStorage.removeItem('token');
      delete api.defaults.headers.common['Authorization'];
    }
  };

  const fetchUser = async () => {
    if (!token.value) return;

    try {
      const response = await api.get('/me');
      user.value = response.data.data;
    } catch (error) {
      console.error('Fetch user error:', error);
      await logout();
    }
  };

  const hasRole = (role: string) => {
    return userRoles.value.includes(role);
  };

  const hasPermission = (permission: string) => {
    return userPermissions.value.includes(permission);
  };

  const hasAnyRole = (roles: string[]) => {
    return roles.some(role => hasRole(role));
  };

  const hasAnyPermission = (permissions: string[]) => {
    return permissions.some(permission => hasPermission(permission));
  };

  // 初始化
  const init = async () => {
    if (token.value) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token.value}`;
      await fetchUser();
    }
  };

  return {
    // 状态
    user,
    token,
    loading,
    // 计算属性
    isAuthenticated,
    userRoles,
    userPermissions,
    // 方法
    login,
    logout,
    fetchUser,
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAnyPermission,
    init
  };
});
