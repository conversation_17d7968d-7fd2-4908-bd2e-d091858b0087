<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'organization_id',
        'status',
        'last_login_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'last_login_at' => 'datetime',
            'status' => 'boolean'
        ];
    }

    /**
     * 所属组织
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * 创建的表单模板
     */
    public function formTemplates(): HasMany
    {
        return $this->hasMany(FormTemplate::class, 'creator_id');
    }

    /**
     * 创建的统计任务
     */
    public function statisticsTasks(): HasMany
    {
        return $this->hasMany(StatisticsTask::class, 'creator_id');
    }

    /**
     * 提交的数据
     */
    public function dataSubmissions(): HasMany
    {
        return $this->hasMany(DataSubmission::class, 'submitter_id');
    }

    /**
     * 审核的数据
     */
    public function reviewedSubmissions(): HasMany
    {
        return $this->hasMany(DataSubmission::class, 'reviewer_id');
    }

    /**
     * 检查用户是否可以访问指定组织的数据
     */
    public function canAccessOrganization(Organization $organization): bool
    {
        if (!$this->organization) {
            return false;
        }

        // 如果是同一个组织
        if ($this->organization_id === $organization->id) {
            return true;
        }

        // 如果是上级组织，可以访问下级组织数据
        $childrenIds = $this->organization->getAllChildrenIds();
        return in_array($organization->id, $childrenIds);
    }

    /**
     * 获取用户可访问的所有组织ID
     */
    public function getAccessibleOrganizationIds(): array
    {
        if (!$this->organization) {
            return [];
        }

        return $this->organization->getAllChildrenIds();
    }

    /**
     * 更新最后登录时间
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }
}
