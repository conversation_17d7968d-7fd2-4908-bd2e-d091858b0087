import{d as M,m as _,c as o,o as s,x as U,a as e,b as m,t as p,w as $,F as x,p as g,r as F,n as L,e as b,v,z as q,q as B,l as I}from"./app-D0Qwllno.js";import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";const z={class:"flex items-center justify-between mb-3"},S={class:"flex items-center space-x-2"},D={class:"h-4 w-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},A={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h7"},E={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"},P={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 20l4-16m2 16l4-16M6 9h14M4 15h14"},N={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 9l4-4 4 4m0 6l-4 4-4-4"},O={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},T={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},H={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"},G={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"},J={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h7"},K={class:"text-sm font-medium text-gray-900"},Q={key:0,class:"text-red-500 text-xs"},R={class:"flex items-center space-x-1"},W=["disabled"],X=["disabled"],Y={class:"space-y-2"},Z={key:0},ee=["placeholder"],te={key:1},oe=["placeholder"],se={key:2},re=["placeholder"],le={key:3},ne={class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:""},de=["value"],ie={key:4,class:"space-y-2"},ae=["id"],ue=["for"],pe={key:5,class:"space-y-2"},me=["name","id"],be=["for"],ce={key:6},ye={key:7},ve={key:8,class:"text-xs text-gray-500 mt-1"},xe=M({__name:"FormFieldEditor",props:{field:{},index:{},totalFields:{default:0},selected:{type:Boolean,default:!1}},emits:["update","delete","moveUp","moveDown","select"],setup(h,{emit:c}){const d=h,r=c,f=_(()=>d.selected),n=()=>{r("select",d.field)},k=()=>{confirm("确定要删除这个字段吗？")&&r("delete",d.index)};return(l,u)=>(s(),o("div",{class:U(["border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors",{"border-blue-500 bg-blue-50":f.value}]),onClick:n},[e("div",z,[e("div",S,[(s(),o("svg",D,[l.field.type==="text"?(s(),o("path",A)):l.field.type==="textarea"?(s(),o("path",E)):l.field.type==="number"?(s(),o("path",P)):l.field.type==="select"?(s(),o("path",N)):l.field.type==="checkbox"?(s(),o("path",O)):l.field.type==="radio"?(s(),o("path",T)):l.field.type==="date"?(s(),o("path",H)):l.field.type==="file"?(s(),o("path",G)):(s(),o("path",J))])),e("span",K,p(l.field.label),1),l.field.required?(s(),o("span",Q,"*")):m("",!0)]),e("div",R,[e("button",{onClick:u[0]||(u[0]=$(i=>l.$emit("moveUp",l.index),["stop"])),disabled:l.index===0,class:"p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50",title:"上移"},u[2]||(u[2]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"})],-1)]),8,W),e("button",{onClick:u[1]||(u[1]=$(i=>l.$emit("moveDown",l.index),["stop"])),disabled:l.index===l.totalFields-1,class:"p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50",title:"下移"},u[3]||(u[3]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1)]),8,X),e("button",{onClick:$(k,["stop"]),class:"p-1 text-red-400 hover:text-red-600",title:"删除"},u[4]||(u[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]))])]),e("div",Y,[l.field.type==="text"?(s(),o("div",Z,[e("input",{type:"text",placeholder:l.field.placeholder,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:""},null,8,ee)])):l.field.type==="textarea"?(s(),o("div",te,[e("textarea",{placeholder:l.field.placeholder,rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:""},null,8,oe)])):l.field.type==="number"?(s(),o("div",se,[e("input",{type:"number",placeholder:l.field.placeholder,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:""},null,8,re)])):l.field.type==="select"?(s(),o("div",le,[e("select",ne,[u[5]||(u[5]=e("option",{value:""},"请选择",-1)),(s(!0),o(x,null,g(l.field.options,i=>(s(),o("option",{key:i,value:i},p(i),9,de))),128))])])):l.field.type==="checkbox"?(s(),o("div",ie,[(s(!0),o(x,null,g(l.field.options,i=>(s(),o("div",{key:i,class:"flex items-center"},[e("input",{type:"checkbox",id:`${l.field.id}_${i}`,class:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",disabled:""},null,8,ae),e("label",{for:`${l.field.id}_${i}`,class:"ml-2 text-sm text-gray-700"},p(i),9,ue)]))),128))])):l.field.type==="radio"?(s(),o("div",pe,[(s(!0),o(x,null,g(l.field.options,i=>(s(),o("div",{key:i,class:"flex items-center"},[e("input",{type:"radio",name:l.field.id,id:`${l.field.id}_${i}`,class:"h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500",disabled:""},null,8,me),e("label",{for:`${l.field.id}_${i}`,class:"ml-2 text-sm text-gray-700"},p(i),9,be)]))),128))])):l.field.type==="date"?(s(),o("div",ce,u[6]||(u[6]=[e("input",{type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:""},null,-1)]))):l.field.type==="file"?(s(),o("div",ye,u[7]||(u[7]=[e("div",{class:"border-2 border-dashed border-gray-300 rounded-lg p-4 text-center"},[e("svg",{class:"mx-auto h-8 w-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})]),e("p",{class:"mt-2 text-sm text-gray-500"},"点击上传文件或拖拽文件到此处")],-1)]))):m("",!0),l.field.description?(s(),o("div",ve,p(l.field.description),1)):m("",!0)])],2))}}),Ut=V(xe,[["__scopeId","data-v-09dd2bfe"]]),ge={class:"space-y-4"},fe={class:"space-y-3"},ke={key:0},he={class:"flex items-center"},we={key:0,class:"border-t pt-4"},$e={class:"space-y-2"},_e=["onUpdate:modelValue"],Me=["onClick"],Ce={key:1,class:"border-t pt-4"},qe={class:"space-y-3"},Ve={class:"grid grid-cols-2 gap-2"},je={key:2,class:"border-t pt-4"},Ue={class:"space-y-3"},Fe={class:"grid grid-cols-2 gap-2"},Le={key:0},Be={key:3,class:"border-t pt-4"},Ie={class:"space-y-3"},ze={class:"flex items-center"},Se={class:"border-t pt-4"},De={class:"space-y-3"},Ae=["value"],Ft=M({__name:"FieldPropertyEditor",props:{field:{}},emits:["update"],setup(h,{emit:c}){const d=h,r=c,f=_(()=>["select","checkbox","radio"].includes(d.field.type)),n=F({...d.field});n.value.validation||(n.value.validation={}),!n.value.options&&f.value&&(n.value.options=["选项1","选项2"]),L(()=>d.field,y=>{n.value={...y},n.value.validation||(n.value.validation={})},{deep:!0});const k=_(()=>["text","textarea","number"].includes(d.field.type)),l=()=>{r("update",{...n.value})},u=()=>{n.value.options||(n.value.options=[]),n.value.options.push(`选项${n.value.options.length+1}`),l()},i=y=>{n.value.options&&n.value.options.length>1&&(n.value.options.splice(y,1),l())},j=y=>({text:"单行文本",textarea:"多行文本",number:"数字",select:"下拉选择",checkbox:"多选框",radio:"单选框",date:"日期",file:"文件上传"})[y]||y;return(y,t)=>(s(),o("div",ge,[e("div",null,[t[18]||(t[18]=e("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"基本属性",-1)),e("div",fe,[e("div",null,[t[14]||(t[14]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"字段标签",-1)),b(e("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>n.value.label=a),type:"text",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:l},null,544),[[v,n.value.label]])]),k.value?(s(),o("div",ke,[t[15]||(t[15]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"占位符",-1)),b(e("input",{"onUpdate:modelValue":t[1]||(t[1]=a=>n.value.placeholder=a),type:"text",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:l},null,544),[[v,n.value.placeholder]])])):m("",!0),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"字段描述",-1)),b(e("textarea",{"onUpdate:modelValue":t[2]||(t[2]=a=>n.value.description=a),rows:"2",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:l},null,544),[[v,n.value.description]])]),e("div",he,[b(e("input",{"onUpdate:modelValue":t[3]||(t[3]=a=>n.value.required=a),type:"checkbox",class:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",onChange:l},null,544),[[q,n.value.required]]),t[17]||(t[17]=e("label",{class:"ml-2 text-xs text-gray-700"},"必填项",-1))])])]),f.value?(s(),o("div",we,[t[20]||(t[20]=e("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"选项配置",-1)),e("div",$e,[(s(!0),o(x,null,g(n.value.options,(a,w)=>(s(),o("div",{key:w,class:"flex items-center space-x-2"},[b(e("input",{"onUpdate:modelValue":C=>n.value.options[w]=C,type:"text",class:"flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:l},null,40,_e),[[v,n.value.options[w]]]),e("button",{onClick:C=>i(w),class:"p-1 text-red-400 hover:text-red-600",title:"删除选项"},t[19]||(t[19]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Me)]))),128)),e("button",{onClick:u,class:"w-full px-2 py-1 text-sm text-blue-600 border border-blue-300 border-dashed rounded hover:bg-blue-50"}," + 添加选项 ")])])):m("",!0),y.field.type==="number"?(s(),o("div",Ce,[t[25]||(t[25]=e("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"数字配置",-1)),e("div",qe,[e("div",Ve,[e("div",null,[t[21]||(t[21]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"最小值",-1)),b(e("input",{"onUpdate:modelValue":t[4]||(t[4]=a=>n.value.validation.min=a),type:"number",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:l},null,544),[[v,n.value.validation.min,void 0,{number:!0}]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"最大值",-1)),b(e("input",{"onUpdate:modelValue":t[5]||(t[5]=a=>n.value.validation.max=a),type:"number",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:l},null,544),[[v,n.value.validation.max,void 0,{number:!0}]])])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"小数位数",-1)),b(e("select",{"onUpdate:modelValue":t[6]||(t[6]=a=>n.value.validation.precision=a),class:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onChange:l},t[23]||(t[23]=[e("option",{value:0},"整数",-1),e("option",{value:1},"1位小数",-1),e("option",{value:2},"2位小数",-1),e("option",{value:3},"3位小数",-1)]),544),[[B,n.value.validation.precision,void 0,{number:!0}]])])])])):m("",!0),["text","textarea"].includes(y.field.type)?(s(),o("div",je,[t[29]||(t[29]=e("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"文本配置",-1)),e("div",Ue,[e("div",Fe,[e("div",null,[t[26]||(t[26]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"最小长度",-1)),b(e("input",{"onUpdate:modelValue":t[7]||(t[7]=a=>n.value.validation.minLength=a),type:"number",min:"0",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:l},null,544),[[v,n.value.validation.minLength,void 0,{number:!0}]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"最大长度",-1)),b(e("input",{"onUpdate:modelValue":t[8]||(t[8]=a=>n.value.validation.maxLength=a),type:"number",min:"1",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:l},null,544),[[v,n.value.validation.maxLength,void 0,{number:!0}]])])]),y.field.type==="textarea"?(s(),o("div",Le,[t[28]||(t[28]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"显示行数",-1)),b(e("input",{"onUpdate:modelValue":t[9]||(t[9]=a=>n.value.rows=a),type:"number",min:"1",max:"10",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:l},null,544),[[v,n.value.rows,void 0,{number:!0}]])])):m("",!0)])])):m("",!0),y.field.type==="file"?(s(),o("div",Be,[t[33]||(t[33]=e("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"文件配置",-1)),e("div",Ie,[e("div",null,[t[30]||(t[30]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"允许的文件类型",-1)),b(e("input",{"onUpdate:modelValue":t[10]||(t[10]=a=>n.value.validation.accept=a),type:"text",placeholder:"例如: .jpg,.png,.pdf",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:l},null,544),[[v,n.value.validation.accept]])]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"最大文件大小 (MB)",-1)),b(e("input",{"onUpdate:modelValue":t[11]||(t[11]=a=>n.value.validation.maxSize=a),type:"number",min:"1",max:"100",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:l},null,544),[[v,n.value.validation.maxSize,void 0,{number:!0}]])]),e("div",ze,[b(e("input",{"onUpdate:modelValue":t[12]||(t[12]=a=>n.value.validation.multiple=a),type:"checkbox",class:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",onChange:l},null,544),[[q,n.value.validation.multiple]]),t[32]||(t[32]=e("label",{class:"ml-2 text-xs text-gray-700"},"允许多文件上传",-1))])])])):m("",!0),e("div",Se,[t[36]||(t[36]=e("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"高级配置",-1)),e("div",De,[e("div",null,[t[34]||(t[34]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"字段ID",-1)),b(e("input",{"onUpdate:modelValue":t[13]||(t[13]=a=>n.value.id=a),type:"text",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded bg-gray-50",readonly:""},null,512),[[v,n.value.id]])]),e("div",null,[t[35]||(t[35]=e("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"字段类型",-1)),e("input",{value:j(y.field.type),type:"text",class:"w-full px-2 py-1 text-sm border border-gray-300 rounded bg-gray-50",readonly:""},null,8,Ae)])])])]))}}),Ee={class:"fixed inset-0 z-50 overflow-y-auto"},Pe={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Ne={class:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"},Oe={class:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Te={class:"flex items-center justify-between mb-4"},He={class:"text-lg leading-6 font-medium text-gray-900"},Ge={class:"mb-6 p-4 bg-gray-50 rounded-lg"},Je={class:"grid grid-cols-2 gap-4 text-sm"},Ke={class:"text-gray-900"},Qe={class:"text-gray-900"},Re={class:"col-span-2"},We={class:"text-gray-900"},Xe={class:"text-gray-900"},Ye={class:"max-h-96 overflow-y-auto"},Ze={class:"block text-sm font-medium text-gray-700"},et={key:0,class:"text-red-500"},tt={key:0,class:"text-xs text-gray-500 -mt-1"},ot=["placeholder","required"],st=["placeholder","rows","required"],rt=["placeholder","required","min","max","step"],lt=["required"],nt=["value"],dt={key:5,class:"space-y-2"},it=["id","value"],at=["for"],ut={key:6,class:"space-y-2"},pt=["name","id","value"],mt=["for"],bt=["required"],ct={key:8},yt={class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors"},vt={class:"mt-4"},xt={class:"cursor-pointer"},gt=["required","accept","multiple"],ft={key:0,class:"mt-2 text-xs text-gray-500"},kt={key:0},ht={key:1},wt={key:2},$t={key:9,class:"text-xs text-gray-500"},_t={key:0},Mt={key:1},Ct={class:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},qt=M({__name:"FormPreviewModal",props:{template:{}},emits:["close"],setup(h){return(c,d)=>(s(),o("div",Ee,[e("div",Pe,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:d[0]||(d[0]=r=>c.$emit("close"))}),e("div",Ne,[e("div",Oe,[e("div",Te,[e("h3",He," 表单预览 - "+p(c.template.name),1),e("button",{onClick:d[1]||(d[1]=r=>c.$emit("close")),class:"text-gray-400 hover:text-gray-600"},d[4]||(d[4]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Ge,[e("div",Je,[e("div",null,[d[5]||(d[5]=e("span",{class:"font-medium text-gray-700"},"模板名称：",-1)),e("span",Ke,p(c.template.name),1)]),e("div",null,[d[6]||(d[6]=e("span",{class:"font-medium text-gray-700"},"分类：",-1)),e("span",Qe,p(c.template.category),1)]),e("div",Re,[d[7]||(d[7]=e("span",{class:"font-medium text-gray-700"},"描述：",-1)),e("span",We,p(c.template.description||"无描述"),1)]),e("div",null,[d[8]||(d[8]=e("span",{class:"font-medium text-gray-700"},"字段数量：",-1)),e("span",Xe,p(c.template.config.fields.length),1)])])]),e("div",Ye,[e("form",{class:"space-y-6",onSubmit:d[2]||(d[2]=$(()=>{},["prevent"]))},[(s(!0),o(x,null,g(c.template.config.fields,r=>{var f,n,k,l,u;return s(),o("div",{key:r.id,class:"space-y-2"},[e("label",Ze,[I(p(r.label)+" ",1),r.required?(s(),o("span",et,"*")):m("",!0)]),r.description?(s(),o("p",tt,p(r.description),1)):m("",!0),r.type==="text"?(s(),o("input",{key:1,type:"text",placeholder:r.placeholder,required:r.required,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,8,ot)):r.type==="textarea"?(s(),o("textarea",{key:2,placeholder:r.placeholder,rows:r.rows||3,required:r.required,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,8,st)):r.type==="number"?(s(),o("input",{key:3,type:"number",placeholder:r.placeholder,required:r.required,min:(f=r.validation)==null?void 0:f.min,max:(n=r.validation)==null?void 0:n.max,step:(k=r.validation)!=null&&k.precision?Math.pow(10,-r.validation.precision):1,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,8,rt)):r.type==="select"?(s(),o("select",{key:4,required:r.required,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[d[9]||(d[9]=e("option",{value:""},"请选择",-1)),(s(!0),o(x,null,g(r.options,i=>(s(),o("option",{key:i,value:i},p(i),9,nt))),128))],8,lt)):r.type==="checkbox"?(s(),o("div",dt,[(s(!0),o(x,null,g(r.options,i=>(s(),o("div",{key:i,class:"flex items-center"},[e("input",{type:"checkbox",id:`preview_${r.id}_${i}`,value:i,class:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"},null,8,it),e("label",{for:`preview_${r.id}_${i}`,class:"ml-2 text-sm text-gray-700"},p(i),9,at)]))),128))])):r.type==="radio"?(s(),o("div",ut,[(s(!0),o(x,null,g(r.options,i=>(s(),o("div",{key:i,class:"flex items-center"},[e("input",{type:"radio",name:`preview_${r.id}`,id:`preview_${r.id}_${i}`,value:i,class:"h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"},null,8,pt),e("label",{for:`preview_${r.id}_${i}`,class:"ml-2 text-sm text-gray-700"},p(i),9,mt)]))),128))])):r.type==="date"?(s(),o("input",{key:7,type:"date",required:r.required,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,8,bt)):r.type==="file"?(s(),o("div",ct,[e("div",yt,[d[12]||(d[12]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1)),e("div",vt,[e("label",xt,[d[10]||(d[10]=e("span",{class:"text-sm text-blue-600 hover:text-blue-500"},"点击上传文件",-1)),e("input",{type:"file",class:"hidden",required:r.required,accept:(l=r.validation)==null?void 0:l.accept,multiple:(u=r.validation)==null?void 0:u.multiple},null,8,gt)]),d[11]||(d[11]=e("p",{class:"text-xs text-gray-500 mt-1"},"或拖拽文件到此处",-1))]),r.validation?(s(),o("div",ft,[r.validation.accept?(s(),o("div",kt," 支持格式："+p(r.validation.accept),1)):m("",!0),r.validation.maxSize?(s(),o("div",ht," 最大大小："+p(r.validation.maxSize)+"MB ",1)):m("",!0),r.validation.multiple?(s(),o("div",wt," 支持多文件上传 ")):m("",!0)])):m("",!0)])])):m("",!0),r.validation?(s(),o("div",$t,[r.validation.minLength||r.validation.maxLength?(s(),o("div",_t," 长度限制："+p(r.validation.minLength||0)+" - "+p(r.validation.maxLength||"无限制")+" 个字符 ",1)):m("",!0),r.validation.min!==void 0||r.validation.max!==void 0?(s(),o("div",Mt," 数值范围："+p(r.validation.min||"无限制")+" - "+p(r.validation.max||"无限制"),1)):m("",!0)])):m("",!0)])}),128)),d[13]||(d[13]=e("div",{class:"pt-4 border-t"},[e("button",{type:"submit",class:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"}," 提交表单 ")],-1))],32)])]),e("div",Ct,[e("button",{onClick:d[3]||(d[3]=r=>c.$emit("close")),type:"button",class:"w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"}," 关闭 ")])])])]))}}),Lt=V(qt,[["__scopeId","data-v-ef773425"]]);export{Lt as F,Ft as _,Ut as a};
