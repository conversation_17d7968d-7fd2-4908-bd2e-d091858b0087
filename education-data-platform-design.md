
# 教育数据统计管理平台设计方案

## 🏗️ 一、平台架构概述

| 层级 | 职能 |
|------|------|
| 区县教育局 | 统筹平台运行、管理各股室、制定统计任务 |
| 学区中心校 | 管理下属中小学填报任务、数据审查 |
| 中小学/幼儿园 | 接收任务并填报数据 |

## 🧱 二、功能模块设计（按业务逻辑分类）

### 1️⃣ 统计表定制系统（模板设计模块）

**功能说明：**
用于各股室用户自定义创建可复用的统计表模板。

**核心功能：**
- 支持字段类型：文本、数值、日期、下拉、多选、附件、人员信息等
- 条件逻辑控制：如“如果选择‘是’，则显示后续字段”
- 字段计算关系：如“总人数 = 男生 + 女生”
- 字段校验规则：正则表达式、数值范围、不能为空等
- 模板版本控制：模板修改记录留存
- 支持字段分组与分类页签设计（便于多栏表设计）
- 支持引入“系统基础字段”：如“学校名称、学段、联系人”等

**技术实现建议：**
- 前端：动态表单生成组件 + 拖拽式字段布局（参考低代码平台）
- 后端：字段配置存于 JSON 字段，动态解析渲染逻辑
- 使用 JSON Schema 或自定义 DSL（Domain Specific Language）作为模板描述语言

### 2️⃣ 任务下发管理模块

**功能说明：**
模板创建后，通过任务下发将统计任务发给学区或学校。

**核心功能：**
- 选择下发单位：按行政区划/组织架构树/自定义单位分组
- 设置填报时间、提交次数限制、催办策略
- 支持“预览填报表”功能，让管理员核对表单结构
- 下发后自动同步给目标单位的“填报任务列表”
- 模板与任务解绑，防止任务期间模板变更影响数据

**技术实现建议：**
- 后端：任务表 task_templates + 任务记录 task_instances
- 支持任务状态跟踪（未开始/进行中/截止/已归档）

### 3️⃣ 数据填报与审核模块

**功能说明：**
学校端用户完成数据填写，提交后支持多级审核。

**核心功能：**
- 按表单自动生成填写界面，字段支持校验
- 保存草稿、正式提交、提交后不可修改（或管理员设置可修改）
- 提交后进入审核流程：学校填 → 学区中心校审核 → 区县终审（可配置多级）
- 审核记录：意见 + 时间戳 + 审核人身份
- 附件上传支持 OCR 识别辅助校验（可扩展）

**技术实现建议：**
- 后端数据存储结构：task_submissions（任务ID、学校ID、数据JSON、状态）
- 异常数据标记机制：如字段异常波动、与历史数据差异过大

### 4️⃣ 填报进度与催办监控模块

**功能说明：**
按区域/单位/任务维度查看填报状态和完成率。

**核心功能：**
- 查看任务发出后的总体完成率（柱状图、表格）
- 可按模板 → 学区 → 学校 逐级下钻
- 未填报学校显示提醒按钮，支持平台提醒/短信提醒
- 支持导出完成进度统计表

### 5️⃣ 数据分析与可视化报表模块

**功能说明：**
将数据汇总为图表、趋势分析、对比报表等形式。

**核心功能：**
- 动态字段汇总统计（如：人数、金额等求和/平均）
- 可设置分组字段：如“学区”、“学校类型”、“学段”等
- 对比分析：支持多期数据对比（如：上学期 vs 本学期）
- 支持图表类型：柱状图、折线图、堆叠图、雷达图、仪表盘、地图视图
- 可导出为 Word、PDF、Excel 图文并茂报表

**技术建议：**
- 前端图表引擎：ECharts + vue-echarts
- 后端使用 Laravel 查询构造器按字段动态聚合

### 6️⃣ 系统管理模块

**功能说明：**
系统级设置与安全控制后台。

**核心功能：**
- 用户管理：用户注册、角色分配、单位归属
- 权限系统：基于 RBAC，支持按钮级别控制
- 操作日志：所有重要操作记录（数据导出、审核、修改等）
- 参数配置：如上传文件大小、系统通知模板、登录方式等
- 数据备份与恢复
- 支持二次登录验证机制（验证码/短信可选）

### 7️⃣ 移动端 / 小程序模块（可选）

- 填报端兼容移动端表单填写
- 审核端支持快速查看审核进度、批量处理
- 使用 Vue3 + Vant3 或开发微信小程序版本

### 8️⃣ 第三方系统对接与数据导入

**功能需求：**
- 可对接已有的教育局基础数据系统
- 支持 Excel 批量导入：按模板字段匹配导入数据
- 支持标准接口（OpenAPI）对接其他业务系统

## 🧠 三、创新功能建议

| 创新功能 | 描述 |
|----------|------|
| 智能填报助手 | 根据历史数据自动补全部分字段，降低填报负担 |
| 智能审核规则 | 可配置“异常检测规则” |
| 填报日历 | 展示单位所有待办任务和截止时间 |
| 定时统计任务 | 支持周期性任务自动生成并下发 |
| 任务模板库 | 支持各类任务预设模板库复用 |

## 🧾 四、数据库结构设计建议（部分）

- `organizations`：组织机构
- `users`：用户表
- `form_templates`：表单模板
- `form_fields`：模板字段
- `tasks`：下发任务
- `submissions`：填报数据
- `reviews`：审核记录
- `statistics`：系统日志/导出记录
- `notifications`：通知表

## ⚙️ 五、开发建议与实现提示

| 层 | 技术建议 |
|----|----------|
| 前端 | Vue 3 + TS + Element Plus + Vue Router + Pinia + ECharts + Axios |
| 后端 | Laravel 12 + Sanctum/JWT + Eloquent ORM + Redis + Task Scheduling |
| 构建部署 | Docker + Supervisor + nginx + 云/局域部署 |
| 开发模式 | 模块热插拔、低耦合结构、统一 API 路由 `/api/v1/...` |
