import{d as E,A as q,r as c,g as I,C as L,s as y,f as P,c as i,y as k,a as t,B as b,b as _,i as w,j as R,k as O,t as p,e as h,v as M,q as G,F as C,p as V,o as n,l as J}from"./app-D0Qwllno.js";import{_ as K,F as Q,a as W}from"./FormPreviewModal-BS2pnpF_.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const X={class:"min-h-screen bg-gray-50"},Y={key:0,class:"flex items-center justify-center min-h-screen"},Z={key:1},T={class:"bg-white shadow"},ee={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},te={class:"flex justify-between h-16"},oe={class:"flex items-center space-x-4"},se={class:"text-xl font-semibold text-gray-900"},le={class:"flex items-center space-x-4"},ne=["disabled"],ie={key:0,class:"animate-spin h-4 w-4",fill:"none",viewBox:"0 0 24 24"},re={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},ae={class:"flex gap-6"},de={class:"w-1/4 space-y-6"},ce={class:"bg-white rounded-lg shadow p-6"},pe={class:"space-y-4"},ue={class:"bg-white rounded-lg shadow p-6"},me={class:"space-y-2"},he=["onClick"],ge={class:"flex-shrink-0 mr-3"},ve={class:"h-5 w-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},fe={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h7"},xe={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"},ye={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 20l4-16m2 16l4-16M6 9h14M4 15h14"},ke={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 9l4-4 4 4m0 6l-4 4-4-4"},be={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},_e={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},we={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"},Me={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"},Ce={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h7"},Ve={class:"text-sm font-medium text-gray-900"},je={class:"text-xs text-gray-500"},Fe={class:"flex-1"},Be={class:"bg-white rounded-lg shadow"},ze={class:"p-6 min-h-96"},Ue={key:0,class:"text-center py-12"},$e={key:1,class:"space-y-4"},De={class:"w-1/4"},Ne={class:"bg-white rounded-lg shadow p-6"},Se={key:0},Ae={key:1,class:"text-center py-8"},Le=E({__name:"Edit",setup(He){const g=P(),j=L(),o=q({name:"",category:"",description:"",config:{fields:[]}}),a=c(null),u=c(!0),d=c(!1),m=c(!1),F=[{type:"text",label:"单行文本",description:"输入单行文本内容"},{type:"textarea",label:"多行文本",description:"输入多行文本内容"},{type:"number",label:"数字",description:"输入数字"},{type:"select",label:"下拉选择",description:"从选项中选择一个"},{type:"checkbox",label:"多选框",description:"选择多个选项"},{type:"radio",label:"单选框",description:"选择一个选项"},{type:"date",label:"日期",description:"选择日期"},{type:"file",label:"文件上传",description:"上传文件"}];I(async()=>{await B()});const B=async()=>{try{u.value=!0;const s=j.params.id,r=(await y.get(`/api/v1/form-templates/${s}`)).data.data;Object.assign(o,{id:r.id,name:r.name,category:r.category,description:r.description,config:r.config||{fields:[]}})}catch(s){console.error("加载模板失败:",s),alert("加载模板失败，请重试"),g.push("/form-templates")}finally{u.value=!1}},z=s=>{const e={id:`field_${Date.now()}`,type:s.type,label:s.label,required:!1,placeholder:`请输入${s.label}`};["select","checkbox","radio"].includes(s.type)&&(e.options=["选项1","选项2","选项3"]),o.config.fields.push(e),a.value=e},v=(s,e)=>{var r;o.config.fields[s]=e,((r=a.value)==null?void 0:r.id)===e.id&&(a.value=e)},U=s=>{var r;const e=o.config.fields[s];o.config.fields.splice(s,1),((r=a.value)==null?void 0:r.id)===e.id&&(a.value=null)},$=s=>{if(s>0){const e=o.config.fields.splice(s,1)[0];o.config.fields.splice(s-1,0,e)}},D=s=>{if(s<o.config.fields.length-1){const e=o.config.fields.splice(s,1)[0];o.config.fields.splice(s+1,0,e)}},N=s=>{a.value=s},S=s=>{const e=o.config.fields.findIndex(r=>r.id===s.id);e!==-1&&v(e,s)},A=async()=>{if(!o.name||!o.category){alert("请填写模板名称和分类");return}if(o.config.fields.length===0){alert("请至少添加一个字段");return}try{d.value=!0,await y.put(`/api/v1/form-templates/${o.id}`,{name:o.name,category:o.category,description:o.description,config:o.config}),alert("模板更新成功！"),g.push("/form-templates")}catch(s){console.error("保存模板失败:",s),alert("保存模板失败，请重试")}finally{d.value=!1}},H=()=>{m.value=!0};return(s,e)=>{const r=O("router-link");return n(),i("div",X,[u.value?(n(),i("div",Y,e[4]||(e[4]=[k('<div class="text-center"><svg class="animate-spin h-8 w-8 text-blue-600 mx-auto" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p class="mt-2 text-sm text-gray-600">加载中...</p></div>',1)]))):(n(),i("div",Z,[t("nav",T,[t("div",ee,[t("div",te,[t("div",oe,[w(r,{to:"/form-templates",class:"text-indigo-600 hover:text-indigo-500"},{default:R(()=>e[5]||(e[5]=[J(" ← 返回模板列表 ",-1)])),_:1,__:[5]}),t("h1",se," 编辑表单模板 - "+p(o.name),1)]),t("div",le,[t("button",{onClick:A,disabled:d.value,class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"},[d.value?(n(),i("svg",ie,e[6]||(e[6]=[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):_("",!0),t("span",null,p(d.value?"保存中...":"保存更改"),1)],8,ne),t("button",{onClick:H,class:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"},e[7]||(e[7]=[t("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1),t("span",null,"预览",-1)]))])])])]),t("div",re,[t("div",ae,[t("div",de,[t("div",ce,[e[12]||(e[12]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"基本信息",-1)),t("div",pe,[t("div",null,[e[8]||(e[8]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模板名称",-1)),h(t("input",{"onUpdate:modelValue":e[0]||(e[0]=l=>o.name=l),type:"text",placeholder:"请输入模板名称",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[M,o.name]])]),t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"分类",-1)),h(t("select",{"onUpdate:modelValue":e[1]||(e[1]=l=>o.category=l),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},e[9]||(e[9]=[k('<option value="">请选择分类</option><option value="基础信息">基础信息</option><option value="教学统计">教学统计</option><option value="财务统计">财务统计</option><option value="人员统计">人员统计</option><option value="设施统计">设施统计</option>',6)]),512),[[G,o.category]])]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"描述",-1)),h(t("textarea",{"onUpdate:modelValue":e[2]||(e[2]=l=>o.description=l),rows:"3",placeholder:"请输入模板描述",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[M,o.description]])])])]),t("div",ue,[e[13]||(e[13]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"字段组件",-1)),t("div",me,[(n(),i(C,null,V(F,l=>t("div",{key:l.type,onClick:f=>z(l),class:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"},[t("div",ge,[(n(),i("svg",ve,[l.type==="text"?(n(),i("path",fe)):l.type==="textarea"?(n(),i("path",xe)):l.type==="number"?(n(),i("path",ye)):l.type==="select"?(n(),i("path",ke)):l.type==="checkbox"?(n(),i("path",be)):l.type==="radio"?(n(),i("path",_e)):l.type==="date"?(n(),i("path",we)):l.type==="file"?(n(),i("path",Me)):(n(),i("path",Ce))]))]),t("div",null,[t("div",Ve,p(l.label),1),t("div",je,p(l.description),1)])],8,he)),64))])])]),t("div",Fe,[t("div",Be,[e[15]||(e[15]=t("div",{class:"p-6 border-b border-gray-200"},[t("h3",{class:"text-lg font-medium text-gray-900"},"表单设计"),t("p",{class:"text-sm text-gray-500 mt-1"},"拖拽左侧组件到此区域，或点击组件直接添加")],-1)),t("div",ze,[o.config.fields.length===0?(n(),i("div",Ue,e[14]||(e[14]=[t("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),t("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"暂无字段",-1),t("p",{class:"mt-1 text-sm text-gray-500"},"从左侧选择字段组件开始设计表单",-1)]))):(n(),i("div",$e,[(n(!0),i(C,null,V(o.config.fields,(l,f)=>{var x;return n(),b(W,{key:l.id,field:l,index:f,"total-fields":o.config.fields.length,selected:((x=a.value)==null?void 0:x.id)===l.id,onUpdate:v,onDelete:U,onMoveUp:$,onMoveDown:D,onSelect:N},null,8,["field","index","total-fields","selected"])}),128))]))])])]),t("div",De,[t("div",Ne,[e[17]||(e[17]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"字段属性",-1)),a.value?(n(),i("div",Se,[w(K,{field:a.value,onUpdate:S},null,8,["field"])])):(n(),i("div",Ae,e[16]||(e[16]=[t("svg",{class:"mx-auto h-8 w-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})],-1),t("p",{class:"mt-2 text-sm text-gray-500"},"选择字段以编辑属性",-1)])))])])])]),m.value?(n(),b(Q,{key:0,template:o,onClose:e[3]||(e[3]=l=>m.value=!1)},null,8,["template"])):_("",!0)]))])}}});export{Le as default};
