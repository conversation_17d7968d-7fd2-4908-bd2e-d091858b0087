import './bootstrap';
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import router from './router';
import App from './App.vue';
import { useAuthStore } from './stores/auth';

// 创建应用实例
const app = createApp(App);

// 使用插件
const pinia = createPinia();
app.use(pinia);
app.use(router);

// 初始化认证状态
const authStore = useAuthStore();
authStore.init().then(() => {
  // 挂载应用
  app.mount('#app');
});
