<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\StatisticsTask;
use App\Models\FormTemplate;
use App\Models\Organization;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StatisticsTaskController extends Controller
{
    /**
     * 获取统计任务列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = StatisticsTask::with(['formTemplate:id,name,category', 'creator:id,name'])
                ->orderBy('created_at', 'desc');

            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // 优先级筛选
            if ($request->filled('priority')) {
                $query->where('priority', $request->priority);
            }

            // 搜索
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('instructions', 'like', "%{$search}%");
                });
            }

            // 分页
            $perPage = $request->get('per_page', 15);
            $tasks = $query->paginate($perPage);

            // 格式化数据
            $tasks->getCollection()->transform(function ($task) {
                return [
                    'id' => $task->id,
                    'title' => $task->title,
                    'description' => $task->instructions,
                    'status' => $task->status,
                    'priority' => $task->priority,
                    'deadline' => $task->end_time?->format('Y-m-d H:i:s'),
                    'completion_rate' => (float) $task->completion_rate,
                    'target_organizations_count' => count($task->target_organizations ?? []),
                    'form_template' => $task->formTemplate ? [
                        'id' => $task->formTemplate->id,
                        'name' => $task->formTemplate->name,
                        'category' => $task->formTemplate->category,
                    ] : null,
                    'creator' => $task->creator ? [
                        'id' => $task->creator->id,
                        'name' => $task->creator->name,
                    ] : null,
                    'start_time' => $task->start_time?->format('Y-m-d H:i:s'),
                    'end_time' => $task->end_time?->format('Y-m-d H:i:s'),
                    'auto_reminder' => $task->auto_reminder,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $task->updated_at->format('Y-m-d H:i:s'),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $tasks->items(),
                'meta' => [
                    'current_page' => $tasks->currentPage(),
                    'last_page' => $tasks->lastPage(),
                    'per_page' => $tasks->perPage(),
                    'total' => $tasks->total(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取统计任务列表失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '获取统计任务列表失败'
            ], 500);
        }
    }

    /**
     * 创建统计任务
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'form_template_id' => 'required|exists:form_templates,id',
                'target_organizations' => 'required|array|min:1',
                'target_organizations.*' => 'exists:organizations,id',
                'start_time' => 'required|date|after_or_equal:now',
                'end_time' => 'required|date|after:start_time',
                'priority' => 'required|in:low,normal,high,urgent',
                'auto_reminder' => 'boolean',
                'instructions' => 'nullable|string',
                'attachments' => 'nullable|array',
            ], [
                'title.required' => '任务标题不能为空',
                'title.max' => '任务标题不能超过255个字符',
                'form_template_id.required' => '请选择表单模板',
                'form_template_id.exists' => '选择的表单模板不存在',
                'target_organizations.required' => '请选择目标组织',
                'target_organizations.min' => '至少选择一个目标组织',
                'target_organizations.*.exists' => '选择的组织不存在',
                'start_time.required' => '开始时间不能为空',
                'start_time.after_or_equal' => '开始时间不能早于当前时间',
                'end_time.required' => '结束时间不能为空',
                'end_time.after' => '结束时间必须晚于开始时间',
                'priority.required' => '请选择优先级',
                'priority.in' => '优先级值无效',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }

            // 验证表单模板是否可用
            $formTemplate = FormTemplate::find($request->form_template_id);
            if ($formTemplate->status !== 'published') {
                return response()->json([
                    'success' => false,
                    'message' => '选择的表单模板未发布，无法创建任务'
                ], 422);
            }

            DB::beginTransaction();

            $task = StatisticsTask::create([
                'title' => $request->title,
                'form_template_id' => $request->form_template_id,
                'creator_id' => Auth::id(),
                'target_organizations' => $request->target_organizations,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'priority' => $request->priority,
                'auto_reminder' => $request->get('auto_reminder', true),
                'instructions' => $request->instructions,
                'attachments' => $request->attachments,
                'status' => 'draft',
                'completion_rate' => 0.00,
            ]);

            // 加载关联数据
            $task->load(['formTemplate:id,name,category', 'creator:id,name']);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '统计任务创建成功',
                'data' => [
                    'id' => $task->id,
                    'title' => $task->title,
                    'description' => $task->instructions,
                    'status' => $task->status,
                    'priority' => $task->priority,
                    'deadline' => $task->end_time?->format('Y-m-d H:i:s'),
                    'completion_rate' => (float) $task->completion_rate,
                    'target_organizations_count' => count($task->target_organizations ?? []),
                    'form_template' => $task->formTemplate ? [
                        'id' => $task->formTemplate->id,
                        'name' => $task->formTemplate->name,
                        'category' => $task->formTemplate->category,
                    ] : null,
                    'creator' => $task->creator ? [
                        'id' => $task->creator->id,
                        'name' => $task->creator->name,
                    ] : null,
                    'start_time' => $task->start_time?->format('Y-m-d H:i:s'),
                    'end_time' => $task->end_time?->format('Y-m-d H:i:s'),
                    'auto_reminder' => $task->auto_reminder,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $task->updated_at->format('Y-m-d H:i:s'),
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建统计任务失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '创建统计任务失败'
            ], 500);
        }
    }

    /**
     * 获取单个统计任务详情
     */
    public function show(string $id): JsonResponse
    {
        try {
            $task = StatisticsTask::with([
                'formTemplate:id,name,category,config',
                'creator:id,name',
                'dataSubmissions.organization:id,name',
                'dataSubmissions.submitter:id,name'
            ])->find($id);

            if (!$task) {
                return response()->json([
                    'success' => false,
                    'message' => '统计任务不存在'
                ], 404);
            }

            // 计算最新完成率
            $task->calculateCompletionRate();

            // 获取目标组织信息
            $targetOrganizations = $task->getTargetOrganizationsModels();
            $pendingOrganizations = $task->getPendingOrganizations();

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $task->id,
                    'title' => $task->title,
                    'description' => $task->instructions,
                    'status' => $task->status,
                    'priority' => $task->priority,
                    'deadline' => $task->end_time?->format('Y-m-d H:i:s'),
                    'completion_rate' => (float) $task->completion_rate,
                    'target_organizations_count' => count($task->target_organizations ?? []),
                    'form_template' => $task->formTemplate ? [
                        'id' => $task->formTemplate->id,
                        'name' => $task->formTemplate->name,
                        'category' => $task->formTemplate->category,
                        'config' => $task->formTemplate->config,
                    ] : null,
                    'creator' => $task->creator ? [
                        'id' => $task->creator->id,
                        'name' => $task->creator->name,
                    ] : null,
                    'start_time' => $task->start_time?->format('Y-m-d H:i:s'),
                    'end_time' => $task->end_time?->format('Y-m-d H:i:s'),
                    'auto_reminder' => $task->auto_reminder,
                    'attachments' => $task->attachments,
                    'target_organizations' => $targetOrganizations->map(function ($org) {
                        return [
                            'id' => $org->id,
                            'name' => $org->name,
                            'type' => $org->type,
                            'level' => $org->level,
                        ];
                    }),
                    'pending_organizations' => $pendingOrganizations->map(function ($org) {
                        return [
                            'id' => $org->id,
                            'name' => $org->name,
                            'type' => $org->type,
                            'level' => $org->level,
                        ];
                    }),
                    'submissions' => $task->dataSubmissions->map(function ($submission) {
                        return [
                            'id' => $submission->id,
                            'status' => $submission->status,
                            'organization' => $submission->organization ? [
                                'id' => $submission->organization->id,
                                'name' => $submission->organization->name,
                            ] : null,
                            'submitter' => $submission->submitter ? [
                                'id' => $submission->submitter->id,
                                'name' => $submission->submitter->name,
                            ] : null,
                            'submit_time' => $submission->submit_time?->format('Y-m-d H:i:s'),
                            'review_time' => $submission->review_time?->format('Y-m-d H:i:s'),
                        ];
                    }),
                    'is_expired' => $task->isExpired(),
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $task->updated_at->format('Y-m-d H:i:s'),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取统计任务详情失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '获取统计任务详情失败'
            ], 500);
        }
    }

    /**
     * 更新统计任务
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $task = StatisticsTask::find($id);

            if (!$task) {
                return response()->json([
                    'success' => false,
                    'message' => '统计任务不存在'
                ], 404);
            }

            // 检查任务状态，已发布的任务限制修改
            if ($task->status !== 'draft') {
                return response()->json([
                    'success' => false,
                    'message' => '只能修改草稿状态的任务'
                ], 422);
            }

            $validator = Validator::make($request->all(), [
                'title' => 'sometimes|required|string|max:255',
                'form_template_id' => 'sometimes|required|exists:form_templates,id',
                'target_organizations' => 'sometimes|required|array|min:1',
                'target_organizations.*' => 'exists:organizations,id',
                'start_time' => 'sometimes|required|date|after_or_equal:now',
                'end_time' => 'sometimes|required|date|after:start_time',
                'priority' => 'sometimes|required|in:low,normal,high,urgent',
                'auto_reminder' => 'sometimes|boolean',
                'instructions' => 'nullable|string',
                'attachments' => 'nullable|array',
            ], [
                'title.required' => '任务标题不能为空',
                'title.max' => '任务标题不能超过255个字符',
                'form_template_id.required' => '请选择表单模板',
                'form_template_id.exists' => '选择的表单模板不存在',
                'target_organizations.required' => '请选择目标组织',
                'target_organizations.min' => '至少选择一个目标组织',
                'target_organizations.*.exists' => '选择的组织不存在',
                'start_time.required' => '开始时间不能为空',
                'start_time.after_or_equal' => '开始时间不能早于当前时间',
                'end_time.required' => '结束时间不能为空',
                'end_time.after' => '结束时间必须晚于开始时间',
                'priority.required' => '请选择优先级',
                'priority.in' => '优先级值无效',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }

            // 如果更新了表单模板，验证其是否可用
            if ($request->filled('form_template_id') && $request->form_template_id != $task->form_template_id) {
                $formTemplate = FormTemplate::find($request->form_template_id);
                if ($formTemplate->status !== 'published') {
                    return response()->json([
                        'success' => false,
                        'message' => '选择的表单模板未发布，无法使用'
                    ], 422);
                }
            }

            DB::beginTransaction();

            $updateData = $request->only([
                'title', 'form_template_id', 'target_organizations',
                'start_time', 'end_time', 'priority', 'auto_reminder',
                'instructions', 'attachments'
            ]);

            $task->update($updateData);

            // 加载关联数据
            $task->load(['formTemplate:id,name,category', 'creator:id,name']);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '统计任务更新成功',
                'data' => [
                    'id' => $task->id,
                    'title' => $task->title,
                    'description' => $task->instructions,
                    'status' => $task->status,
                    'priority' => $task->priority,
                    'deadline' => $task->end_time?->format('Y-m-d H:i:s'),
                    'completion_rate' => (float) $task->completion_rate,
                    'target_organizations_count' => count($task->target_organizations ?? []),
                    'form_template' => $task->formTemplate ? [
                        'id' => $task->formTemplate->id,
                        'name' => $task->formTemplate->name,
                        'category' => $task->formTemplate->category,
                    ] : null,
                    'creator' => $task->creator ? [
                        'id' => $task->creator->id,
                        'name' => $task->creator->name,
                    ] : null,
                    'start_time' => $task->start_time?->format('Y-m-d H:i:s'),
                    'end_time' => $task->end_time?->format('Y-m-d H:i:s'),
                    'auto_reminder' => $task->auto_reminder,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $task->updated_at->format('Y-m-d H:i:s'),
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新统计任务失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '更新统计任务失败'
            ], 500);
        }
    }

    /**
     * 删除统计任务
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $task = StatisticsTask::find($id);

            if (!$task) {
                return response()->json([
                    'success' => false,
                    'message' => '统计任务不存在'
                ], 404);
            }

            // 检查任务状态，只能删除草稿状态的任务
            if ($task->status !== 'draft') {
                return response()->json([
                    'success' => false,
                    'message' => '只能删除草稿状态的任务'
                ], 422);
            }

            DB::beginTransaction();

            $task->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '统计任务删除成功'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('删除统计任务失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '删除统计任务失败'
            ], 500);
        }
    }

    /**
     * 发布统计任务
     */
    public function publish(string $id): JsonResponse
    {
        try {
            $task = StatisticsTask::find($id);

            if (!$task) {
                return response()->json([
                    'success' => false,
                    'message' => '统计任务不存在'
                ], 404);
            }

            if ($task->status !== 'draft') {
                return response()->json([
                    'success' => false,
                    'message' => '只能发布草稿状态的任务'
                ], 422);
            }

            // 验证任务数据完整性
            if (empty($task->target_organizations)) {
                return response()->json([
                    'success' => false,
                    'message' => '任务缺少目标组织，无法发布'
                ], 422);
            }

            if (!$task->start_time || !$task->end_time) {
                return response()->json([
                    'success' => false,
                    'message' => '任务缺少时间设置，无法发布'
                ], 422);
            }

            if (!$task->formTemplate || $task->formTemplate->status !== 'published') {
                return response()->json([
                    'success' => false,
                    'message' => '关联的表单模板未发布，无法发布任务'
                ], 422);
            }

            DB::beginTransaction();

            $success = $task->publish();

            if (!$success) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => '任务发布失败'
                ], 500);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '统计任务发布成功',
                'data' => [
                    'id' => $task->id,
                    'status' => $task->status,
                    'updated_at' => $task->updated_at->format('Y-m-d H:i:s'),
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('发布统计任务失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '发布统计任务失败'
            ], 500);
        }
    }

    /**
     * 获取任务进度详情
     */
    public function progress(string $id): JsonResponse
    {
        try {
            $task = StatisticsTask::with([
                'formTemplate:id,name',
                'dataSubmissions.organization:id,name,type',
                'dataSubmissions.submitter:id,name'
            ])->find($id);

            if (!$task) {
                return response()->json([
                    'success' => false,
                    'message' => '统计任务不存在'
                ], 404);
            }

            // 计算最新完成率
            $completionRate = $task->calculateCompletionRate();

            // 获取目标组织和提交情况
            $targetOrganizations = $task->getTargetOrganizationsModels();
            $pendingOrganizations = $task->getPendingOrganizations();

            // 统计各状态的提交数量
            $submissionStats = $task->dataSubmissions()
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();

            $totalTargets = count($task->target_organizations ?? []);
            $submittedCount = $task->dataSubmissions()
                ->whereIn('status', ['submitted', 'approved'])
                ->distinct('organization_id')
                ->count('organization_id');
            $approvedCount = $task->dataSubmissions()
                ->where('status', 'approved')
                ->distinct('organization_id')
                ->count('organization_id');

            return response()->json([
                'success' => true,
                'data' => [
                    'task_info' => [
                        'id' => $task->id,
                        'title' => $task->title,
                        'status' => $task->status,
                        'priority' => $task->priority,
                        'start_time' => $task->start_time?->format('Y-m-d H:i:s'),
                        'end_time' => $task->end_time?->format('Y-m-d H:i:s'),
                        'is_expired' => $task->isExpired(),
                    ],
                    'progress_summary' => [
                        'total_targets' => $totalTargets,
                        'submitted_count' => $submittedCount,
                        'approved_count' => $approvedCount,
                        'pending_count' => $totalTargets - $submittedCount,
                        'completion_rate' => $completionRate,
                    ],
                    'submission_stats' => [
                        'draft' => $submissionStats['draft'] ?? 0,
                        'submitted' => $submissionStats['submitted'] ?? 0,
                        'reviewing' => $submissionStats['reviewing'] ?? 0,
                        'approved' => $submissionStats['approved'] ?? 0,
                        'rejected' => $submissionStats['rejected'] ?? 0,
                    ],
                    'target_organizations' => $targetOrganizations->map(function ($org) use ($task) {
                        $submission = $task->dataSubmissions()
                            ->where('organization_id', $org->id)
                            ->latest()
                            ->first();

                        return [
                            'id' => $org->id,
                            'name' => $org->name,
                            'type' => $org->type,
                            'submission_status' => $submission ? $submission->status : 'not_started',
                            'submit_time' => $submission?->submit_time?->format('Y-m-d H:i:s'),
                            'submitter_name' => $submission?->submitter?->name,
                        ];
                    }),
                    'pending_organizations' => $pendingOrganizations->map(function ($org) {
                        return [
                            'id' => $org->id,
                            'name' => $org->name,
                            'type' => $org->type,
                        ];
                    }),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取任务进度失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '获取任务进度失败'
            ], 500);
        }
    }

    /**
     * 发送任务提醒
     */
    public function remind(string $id): JsonResponse
    {
        try {
            $task = StatisticsTask::find($id);

            if (!$task) {
                return response()->json([
                    'success' => false,
                    'message' => '统计任务不存在'
                ], 404);
            }

            if ($task->status !== 'published' && $task->status !== 'in_progress') {
                return response()->json([
                    'success' => false,
                    'message' => '只能对已发布或进行中的任务发送提醒'
                ], 422);
            }

            // 获取未提交的组织
            $pendingOrganizations = $task->getPendingOrganizations();

            if ($pendingOrganizations->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => '所有目标组织都已提交，无需发送提醒'
                ], 422);
            }

            // TODO: 这里应该实现实际的提醒发送逻辑
            // 比如发送邮件、短信、系统通知等
            // 目前只是记录日志和返回成功响应

            Log::info("任务提醒发送", [
                'task_id' => $task->id,
                'task_title' => $task->title,
                'pending_organizations' => $pendingOrganizations->pluck('name')->toArray(),
                'reminder_time' => now()->format('Y-m-d H:i:s'),
            ]);

            return response()->json([
                'success' => true,
                'message' => '提醒发送成功',
                'data' => [
                    'reminded_organizations_count' => $pendingOrganizations->count(),
                    'reminded_organizations' => $pendingOrganizations->map(function ($org) {
                        return [
                            'id' => $org->id,
                            'name' => $org->name,
                            'type' => $org->type,
                        ];
                    }),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('发送任务提醒失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '发送任务提醒失败'
            ], 500);
        }
    }
}
