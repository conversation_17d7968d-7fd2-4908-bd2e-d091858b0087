<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center space-x-4">
            <router-link to="/dashboard" class="text-indigo-600 hover:text-indigo-500">
              ← 返回首页
            </router-link>
            <h1 class="text-xl font-semibold text-gray-900">
              用户管理
            </h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">
              {{ authStore.user?.name }}
            </span>
            <button
              @click="handleLogout"
              class="text-sm text-gray-500 hover:text-gray-700"
            >
              退出登录
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- 操作栏 -->
        <div class="mb-6 flex justify-between items-center">
          <div class="flex space-x-4">
            <!-- 搜索框 -->
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索用户..."
                class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>

            <!-- 角色筛选 -->
            <select
              v-model="selectedRole"
              class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">所有角色</option>
              <option value="超级管理员">超级管理员</option>
              <option value="系统管理员">系统管理员</option>
              <option value="股室管理员">股室管理员</option>
              <option value="学区管理员">学区管理员</option>
              <option value="学校管理员">学校管理员</option>
              <option value="填报员">填报员</option>
            </select>
          </div>

          <!-- 操作按钮 -->
          <div class="flex space-x-3">
            <button
              @click="downloadUserTemplate"
              class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
            >
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>下载模板</span>
            </button>
            <button
              @click="showImportModal = true"
              class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center space-x-2"
            >
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
              </svg>
              <span>批量导入</span>
            </button>
            <button
              @click="showCreateModal = true"
              class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span>添加用户</span>
            </button>
          </div>
        </div>

        <!-- 用户列表 -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户信息</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属机构</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="user in filteredUsers" :key="user.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                        <span class="text-sm font-medium text-gray-700">{{ user.name.charAt(0) }}</span>
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                      <div class="text-sm text-gray-500">{{ user.email }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ user.organization?.name || '-' }}</div>
                  <div class="text-sm text-gray-500">{{ user.organization?.type || '-' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex flex-wrap gap-1">
                    <span
                      v-for="role in user.roles"
                      :key="role.id"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {{ role.display_name }}
                    </span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    :class="[
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      user.status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ user.status ? '启用' : '禁用' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(user.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button
                      @click="editUser(user)"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      编辑
                    </button>
                    <button
                      @click="toggleUserStatus(user)"
                      :class="[
                        'hover:text-gray-900',
                        user.status ? 'text-red-600' : 'text-green-600'
                      ]"
                    >
                      {{ user.status ? '禁用' : '启用' }}
                    </button>
                    <button
                      @click="resetPassword(user)"
                      class="text-purple-600 hover:text-purple-900"
                    >
                      重置密码
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- 空状态 -->
          <div v-if="filteredUsers.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无用户</h3>
            <p class="mt-1 text-sm text-gray-500">开始添加用户来管理系统访问权限。</p>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.total > pagination.per_page" class="mt-6 flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示 {{ pagination.from }} 到 {{ pagination.to }} 条，共 {{ pagination.total }} 条记录
          </div>
          <div class="flex space-x-2">
            <button
              @click="loadUsers(pagination.current_page - 1)"
              :disabled="pagination.current_page <= 1"
              class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <button
              @click="loadUsers(pagination.current_page + 1)"
              :disabled="pagination.current_page >= pagination.last_page"
              class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑用户模态框 -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ showCreateModal ? '添加用户' : '编辑用户' }}
          </h3>

          <form @submit.prevent="showCreateModal ? createUser() : updateUser()">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">姓名</label>
                <input
                  v-model="userForm.name"
                  type="text"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">邮箱</label>
                <input
                  v-model="userForm.email"
                  type="email"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div v-if="showCreateModal">
                <label class="block text-sm font-medium text-gray-700">密码</label>
                <input
                  v-model="userForm.password"
                  type="password"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">所属机构</label>
                <select
                  v-model="userForm.organization_id"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">请选择机构</option>
                  <option v-for="org in organizations" :key="org.id" :value="org.id">
                    {{ org.name }}
                  </option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">角色</label>
                <div class="mt-2 space-y-2">
                  <label v-for="role in roles" :key="role.id" class="flex items-center">
                    <input
                      v-model="userForm.role_ids"
                      :value="role.id"
                      type="checkbox"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span class="ml-2 text-sm text-gray-700">{{ role.display_name }}</span>
                  </label>
                </div>
              </div>
            </div>

            <div class="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                @click="closeModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
              >
                取消
              </button>
              <button
                type="submit"
                :disabled="loading"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {{ loading ? '保存中...' : '保存' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 批量导入模态框 -->
    <div v-if="showImportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">批量导入用户</h3>

          <div class="mb-4">
            <p class="text-sm text-gray-600 mb-2">请选择Excel文件进行批量导入：</p>
            <input
              type="file"
              accept=".xlsx,.xls"
              @change="handleUserFileSelect"
              class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"
            />
          </div>

          <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p class="text-sm text-yellow-800">
              <strong>导入说明：</strong><br>
              • Excel文件应包含：姓名、邮箱、密码、组织编码、角色列<br>
              • 角色可选：super_admin, system_admin, department_admin, district_admin, school_admin, data_entry<br>
              • 组织编码必须是已存在的组织编码
            </p>
          </div>

          <div class="flex justify-end space-x-3">
            <button
              @click="closeUserImportModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
            >
              取消
            </button>
            <button
              @click="handleUserImport"
              :disabled="!importFile || importing"
              class="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 rounded-md"
            >
              {{ importing ? '导入中...' : '开始导入' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import api from '@/utils/api';
import dayjs from 'dayjs';

interface User {
  id: number;
  name: string;
  email: string;
  status: boolean;
  created_at: string;
  organization?: {
    id: number;
    name: string;
    type: string;
  };
  roles: Array<{
    id: number;
    name: string;
    display_name: string;
  }>;
}

interface Organization {
  id: number;
  name: string;
  type: string;
}

interface Role {
  id: number;
  name: string;
  display_name: string;
}

interface Pagination {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
}

const router = useRouter();
const authStore = useAuthStore();

// 响应式数据
const users = ref<User[]>([]);
const organizations = ref<Organization[]>([]);
const roles = ref<Role[]>([]);
const searchQuery = ref('');
const selectedRole = ref('');
const loading = ref(false);
const showCreateModal = ref(false);
const showEditModal = ref(false);
const showImportModal = ref(false);
const currentUser = ref<User | null>(null);
const importFile = ref<File | null>(null);
const importing = ref(false);

// 分页数据
const pagination = ref<Pagination>({
  current_page: 1,
  last_page: 1,
  per_page: 15,
  total: 0,
  from: 0,
  to: 0,
});

// 用户表单数据
const userForm = ref<{
  name: string;
  email: string;
  password?: string;
  organization_id: string;
  role_ids: number[];
}>({
  name: '',
  email: '',
  password: '',
  organization_id: '',
  role_ids: [],
});

// 计算属性
const filteredUsers = computed(() => {
  let filtered = users.value || [];

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(user =>
      user.name?.toLowerCase().includes(query) ||
      user.email?.toLowerCase().includes(query)
    );
  }

  if (selectedRole.value) {
    filtered = filtered.filter(user =>
      user.roles?.some(role => role.display_name === selectedRole.value)
    );
  }

  return filtered;
});

// 方法
const handleLogout = async () => {
  await authStore.logout();
  router.push('/login');
};

const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm');
};

const loadUsers = async (page = 1) => {
  try {
    loading.value = true;
    const response = await api.get(`/users?page=${page}`);
    users.value = response.data.data;
    pagination.value = {
      current_page: response.data.current_page,
      last_page: response.data.last_page,
      per_page: response.data.per_page,
      total: response.data.total,
      from: response.data.from,
      to: response.data.to,
    };
  } catch (error) {
    console.error('加载用户列表失败:', error);
  } finally {
    loading.value = false;
  }
};

const loadOrganizations = async () => {
  try {
    const response = await api.get('/organizations');
    organizations.value = response.data.data;
  } catch (error) {
    console.error('加载机构列表失败:', error);
  }
};

const loadRoles = async () => {
  try {
    const response = await api.get('/roles');
    roles.value = response.data.data;
  } catch (error) {
    console.error('加载角色列表失败:', error);
  }
};

const createUser = async () => {
  try {
    loading.value = true;
    await api.post('/users', userForm.value);
    showCreateModal.value = false;
    resetForm();
    await loadUsers();
  } catch (error) {
    console.error('创建用户失败:', error);
  } finally {
    loading.value = false;
  }
};

const editUser = (user: User) => {
  currentUser.value = user;
  userForm.value = {
    name: user.name,
    email: user.email,
    password: '',
    organization_id: user.organization?.id?.toString() || '',
    role_ids: user.roles.map(role => role.id),
  };
  showEditModal.value = true;
};

const updateUser = async () => {
  if (!currentUser.value) return;

  try {
    loading.value = true;
    const updateData = { ...userForm.value };
    delete updateData.password; // 编辑时不更新密码

    await api.put(`/users/${currentUser.value.id}`, updateData);
    showEditModal.value = false;
    resetForm();
    await loadUsers();
  } catch (error) {
    console.error('更新用户失败:', error);
  } finally {
    loading.value = false;
  }
};

const toggleUserStatus = async (user: User) => {
  try {
    await api.patch(`/api/v1/users/${user.id}/toggle-status`);
    await loadUsers();
  } catch (error) {
    console.error('切换用户状态失败:', error);
  }
};

const resetPassword = async (user: User) => {
  if (!confirm(`确定要重置用户 ${user.name} 的密码吗？`)) return;

  try {
    const response = await api.post(`/users/${user.id}/reset-password`);
    alert(`密码已重置为: ${response.data.password}`);
  } catch (error) {
    console.error('重置密码失败:', error);
  }
};

const closeModal = () => {
  showCreateModal.value = false;
  showEditModal.value = false;
  resetForm();
};

const resetForm = () => {
  userForm.value = {
    name: '',
    email: '',
    password: '',
    organization_id: '',
    role_ids: [],
  };
  currentUser.value = null;
};

// 批量导入相关方法
const downloadUserTemplate = () => {
  // 创建用户导入模板数据
  const templateData = [
    ['姓名', '邮箱', '密码', '组织编码', '角色'],
    ['张三', '<EMAIL>', '123456', '130182003', 'school_admin'],
    ['李四', '<EMAIL>', '123456', '130182004', 'data_entry'],
  ];

  // 创建CSV内容
  const csvContent = templateData.map(row => row.join(',')).join('\n');
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', '用户导入模板.csv');
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const handleUserFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    importFile.value = target.files[0];
  }
};

const closeUserImportModal = () => {
  showImportModal.value = false;
  importFile.value = null;
};

const handleUserImport = async () => {
  if (!importFile.value) return;

  importing.value = true;
  try {
    const formData = new FormData();
    formData.append('file', importFile.value);

    const response = await api.post('/users/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (response.data.success) {
      alert(`导入成功！共导入 ${response.data.imported_count} 个用户`);
      await loadUsers();
      closeUserImportModal();
    } else {
      alert('导入失败：' + (response.data.message || '未知错误'));
    }
  } catch (error: any) {
    console.error('导入失败:', error);
    alert('导入失败：' + (error.response?.data?.message || error.message || '网络错误'));
  } finally {
    importing.value = false;
  }
};

// 生命周期
onMounted(async () => {
  await Promise.all([
    loadUsers(),
    loadOrganizations(),
    loadRoles(),
  ]);
});
</script>
