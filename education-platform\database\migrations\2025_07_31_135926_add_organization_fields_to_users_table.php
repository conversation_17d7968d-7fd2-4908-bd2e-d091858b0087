<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedBigInteger('organization_id')->nullable()->after('email')->comment('所属组织ID');
            $table->string('phone', 20)->nullable()->after('email')->comment('手机号');
            $table->tinyInteger('status')->default(1)->after('remember_token')->comment('状态：1启用，0禁用');
            $table->timestamp('last_login_at')->nullable()->after('updated_at')->comment('最后登录时间');

            // 索引
            $table->index('organization_id');
            $table->index('status');

            // 外键约束
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['organization_id']);
            $table->dropIndex(['organization_id']);
            $table->dropIndex(['status']);
            $table->dropColumn(['organization_id', 'phone', 'status', 'last_login_at']);
        });
    }
};
