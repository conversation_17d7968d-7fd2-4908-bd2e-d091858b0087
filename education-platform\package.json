{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "@vueuse/core": "^10.7.0", "axios": "^1.8.2", "@headlessui/vue": "^1.7.0", "@heroicons/vue": "^2.0.0", "dayjs": "^1.11.0"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "@types/node": "^20.10.0", "@vitejs/plugin-vue": "^5.2.0", "@vue/tsconfig": "^0.5.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.3.0", "vite": "^6.0.0", "vue-tsc": "^2.0.0"}}