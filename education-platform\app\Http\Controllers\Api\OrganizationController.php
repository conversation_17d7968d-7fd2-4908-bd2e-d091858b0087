<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Organization;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class OrganizationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Organization::with('parent');

        // 搜索过滤
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        // 类型过滤
        if ($request->has('type')) {
            $query->where('type', $request->get('type'));
        }

        // 状态过滤
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // 父级过滤
        if ($request->has('parent_id')) {
            $query->where('parent_id', $request->get('parent_id'));
        }

        $organizations = $query->orderBy('level')
                              ->orderBy('sort_order')
                              ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $organizations
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:organizations,code',
            'type' => 'required|in:county,district,school,kindergarten',
            'parent_id' => 'nullable|exists:organizations,id',
            'contact_info' => 'nullable|array',
            'contact_info.address' => 'nullable|string',
            'contact_info.phone' => 'nullable|string',
            'contact_info.email' => 'nullable|email',
        ]);

        // 根据组织类型确定层级和验证父级关系
        $typeToLevel = [
            'county' => 1,      // 县/区教育局
            'district' => 2,    // 学区中心校
            'school' => 3,      // 学校
            'kindergarten' => 3 // 幼儿园
        ];

        $level = $typeToLevel[$request->type];

        // 验证父级关系的合理性
        if ($request->parent_id) {
            $parent = Organization::find($request->parent_id);
            if (!$parent) {
                return response()->json([
                    'success' => false,
                    'message' => '父级组织不存在'
                ], 422);
            }

            // 验证层级关系
            if ($parent->level >= $level) {
                return response()->json([
                    'success' => false,
                    'message' => '父级组织层级不正确。' . $this->getTypeLabel($request->type) . '应该隶属于' . $this->getValidParentTypes($request->type)
                ], 422);
            }
        } else {
            // 只有县/区教育局可以没有父级
            if ($request->type !== 'county') {
                return response()->json([
                    'success' => false,
                    'message' => $this->getTypeLabel($request->type) . '必须选择父级组织'
                ], 422);
            }
        }

        $organization = Organization::create([
            'name' => $request->name,
            'code' => $request->code,
            'type' => $request->type,
            'parent_id' => $request->parent_id,
            'level' => $level,
            'sort_order' => $request->get('sort_order', 0),
            'contact_info' => $request->get('contact_info'),
            'status' => $request->get('status', 1)
        ]);

        $organization->load('parent');

        return response()->json([
            'success' => true,
            'message' => '组织创建成功',
            'data' => $organization
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Organization $organization): JsonResponse
    {
        $organization->load(['parent', 'children', 'users']);

        return response()->json([
            'success' => true,
            'data' => $organization
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Organization $organization): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => ['required', 'string', 'max:50', Rule::unique('organizations')->ignore($organization->id)],
            'type' => 'required|in:county,district,school,kindergarten',
            'parent_id' => 'nullable|exists:organizations,id',
            'contact_info' => 'nullable|array',
            'contact_info.address' => 'nullable|string',
            'contact_info.phone' => 'nullable|string',
            'contact_info.email' => 'nullable|email',
        ]);

        // 防止设置自己为父级
        if ($request->parent_id == $organization->id) {
            return response()->json([
                'success' => false,
                'message' => '不能将自己设置为父级组织'
            ], 422);
        }

        // 根据组织类型确定层级和验证父级关系
        $typeToLevel = [
            'county' => 1,      // 县/区教育局
            'district' => 2,    // 学区中心校
            'school' => 3,      // 学校
            'kindergarten' => 3 // 幼儿园
        ];

        $level = $typeToLevel[$request->type];

        // 验证父级关系的合理性
        if ($request->parent_id) {
            $parent = Organization::find($request->parent_id);
            if (!$parent) {
                return response()->json([
                    'success' => false,
                    'message' => '父级组织不存在'
                ], 422);
            }

            // 验证层级关系
            if ($parent->level >= $level) {
                return response()->json([
                    'success' => false,
                    'message' => '父级组织层级不正确。' . $this->getTypeLabel($request->type) . '应该隶属于' . $this->getValidParentTypes($request->type)
                ], 422);
            }
        } else {
            // 只有县/区教育局可以没有父级
            if ($request->type !== 'county') {
                return response()->json([
                    'success' => false,
                    'message' => $this->getTypeLabel($request->type) . '必须选择父级组织'
                ], 422);
            }
        }

        $organization->update([
            'name' => $request->name,
            'code' => $request->code,
            'type' => $request->type,
            'parent_id' => $request->parent_id,
            'level' => $level,
            'sort_order' => $request->get('sort_order', $organization->sort_order),
            'contact_info' => $request->get('contact_info', $organization->contact_info),
            'status' => $request->get('status', $organization->status)
        ]);

        $organization->load('parent');

        return response()->json([
            'success' => true,
            'message' => '组织更新成功',
            'data' => $organization
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Organization $organization): JsonResponse
    {
        // 检查是否有子组织
        if ($organization->children()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该组织下还有子组织，无法删除'
            ], 422);
        }

        // 检查是否有用户
        if ($organization->users()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该组织下还有用户，无法删除'
            ], 422);
        }

        $organization->delete();

        return response()->json([
            'success' => true,
            'message' => '组织删除成功'
        ]);
    }

    /**
     * 获取组织的子级
     */
    public function children(Organization $organization): JsonResponse
    {
        $children = $organization->children()
                                ->orderBy('sort_order')
                                ->get();

        return response()->json([
            'success' => true,
            'data' => $children
        ]);
    }

    /**
     * 获取组织树
     */
    public function tree(): JsonResponse
    {
        $organizations = Organization::with('descendants')
                                   ->whereNull('parent_id')
                                   ->orderBy('sort_order')
                                   ->get();

        return response()->json([
            'success' => true,
            'data' => $organizations
        ]);
    }

    /**
     * 获取组织类型的中文标签
     */
    private function getTypeLabel(string $type): string
    {
        $labels = [
            'county' => '县/区教育局',
            'district' => '学区中心校',
            'school' => '学校',
            'kindergarten' => '幼儿园'
        ];

        return $labels[$type] ?? $type;
    }

    /**
     * 获取有效的父级组织类型描述
     */
    private function getValidParentTypes(string $type): string
    {
        $validParents = [
            'district' => '县/区教育局',
            'school' => '学区中心校',
            'kindergarten' => '学区中心校'
        ];

        return $validParents[$type] ?? '';
    }

    /**
     * 批量导入组织
     */
    public function import(Request $request): JsonResponse
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv'
        ]);

        try {
            $file = $request->file('file');
            $extension = $file->getClientOriginalExtension();

            if ($extension === 'csv') {
                $data = $this->parseCsvFile($file);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '暂时只支持CSV格式文件'
                ], 400);
            }

            $importedCount = 0;
            $errors = [];

            foreach ($data as $index => $row) {
                try {
                    // 跳过标题行
                    if ($index === 0) continue;

                    if (count($row) < 3) {
                        $errors[] = "第" . ($index + 1) . "行：数据不完整";
                        continue;
                    }

                    $name = trim($row[0]);
                    $code = trim($row[1]);
                    $type = trim($row[2]);
                    $parentCode = isset($row[3]) ? trim($row[3]) : '';

                    // 验证必填字段
                    if (empty($name) || empty($code) || empty($type)) {
                        $errors[] = "第" . ($index + 1) . "行：名称、编码、类型不能为空";
                        continue;
                    }

                    // 验证类型
                    if (!in_array($type, ['county', 'district', 'school', 'kindergarten'])) {
                        $errors[] = "第" . ($index + 1) . "行：类型必须是 county, district, school, kindergarten 之一";
                        continue;
                    }

                    // 检查编码是否已存在
                    if (Organization::where('code', $code)->exists()) {
                        $errors[] = "第" . ($index + 1) . "行：编码 {$code} 已存在";
                        continue;
                    }

                    // 查找父级组织
                    $parentId = null;
                    if (!empty($parentCode)) {
                        $parent = Organization::where('code', $parentCode)->first();
                        if (!$parent) {
                            $errors[] = "第" . ($index + 1) . "行：找不到父级组织编码 {$parentCode}";
                            continue;
                        }
                        $parentId = $parent->id;
                    }

                    // 确定层级
                    $level = $this->getOrganizationLevel($type);

                    // 创建组织
                    Organization::create([
                        'name' => $name,
                        'code' => $code,
                        'type' => $type,
                        'parent_id' => $parentId,
                        'level' => $level,
                        'sort_order' => 0,
                        'contact_info' => json_encode([
                            'address' => '',
                            'phone' => '',
                            'email' => ''
                        ]),
                        'status' => 1,
                    ]);

                    $importedCount++;
                } catch (\Exception $e) {
                    $errors[] = "第" . ($index + 1) . "行：" . $e->getMessage();
                }
            }

            return response()->json([
                'success' => true,
                'imported_count' => $importedCount,
                'errors' => $errors,
                'message' => "成功导入 {$importedCount} 个组织" . (count($errors) > 0 ? "，{count($errors)} 个错误" : '')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导入失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 解析CSV文件
     */
    private function parseCsvFile($file): array
    {
        $data = [];
        $handle = fopen($file->getPathname(), 'r');

        if ($handle !== false) {
            while (($row = fgetcsv($handle, 1000, ',')) !== false) {
                $data[] = $row;
            }
            fclose($handle);
        }

        return $data;
    }

    /**
     * 根据类型获取组织层级
     */
    private function getOrganizationLevel(string $type): int
    {
        $levelMap = [
            'county' => 1,
            'district' => 2,
            'school' => 3,
            'kindergarten' => 3
        ];

        return $levelMap[$type] ?? 1;
    }
}
