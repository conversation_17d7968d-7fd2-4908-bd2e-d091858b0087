<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organization;

class FixDistrictParents extends Command
{
    protected $signature = 'fix:district-parents';
    protected $description = '修复学区中心校的父级关系';

    public function handle()
    {
        $this->info('开始修复学区中心校的父级关系...');

        // 获取县教育局
        $county = Organization::where('type', 'county')->first();
        if (!$county) {
            $this->error('未找到县教育局组织');
            return 1;
        }

        $this->info("找到县教育局: {$county->name} (ID: {$county->id})");

        // 查找没有父级的学区中心校
        $districts = Organization::where('type', 'district')
            ->whereNull('parent_id')
            ->get();

        if ($districts->isEmpty()) {
            $this->info('没有找到需要修复的学区中心校');
            return 0;
        }

        $this->info("找到 {$districts->count()} 个需要修复的学区中心校");

        foreach ($districts as $district) {
            $this->info("修复: {$district->name} (当前parent_id: {$district->parent_id})");
            $district->parent_id = $county->id;
            $district->save();
            $this->info("已更新: {$district->name} -> 上级: {$county->name}");
        }

        $this->info('修复完成！');
        return 0;
    }
}
