<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center space-x-4">
            <router-link to="/statistics-tasks" class="text-indigo-600 hover:text-indigo-500">
              ← 返回列表
            </router-link>
            <h1 class="text-xl font-semibold text-gray-900">
              创建统计任务
            </h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">
              {{ authStore.user?.name }}
            </span>
            <button
              @click="handleLogout"
              class="text-sm text-gray-500 hover:text-gray-700"
            >
              退出登录
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">基本信息</h2>
            
            <!-- 任务标题 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                任务标题 <span class="text-red-500">*</span>
              </label>
              <input
                v-model="form.title"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="请输入任务标题"
              />
            </div>

            <!-- 表单模板 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                表单模板 <span class="text-red-500">*</span>
              </label>
              <select
                v-model="form.form_template_id"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="">请选择表单模板</option>
                <option
                  v-for="template in formTemplates"
                  :key="template.id"
                  :value="template.id"
                >
                  {{ template.name }}
                </option>
              </select>
            </div>

            <!-- 目标组织 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                目标组织 <span class="text-red-500">*</span>
              </label>
              <div class="space-y-2 max-h-48 overflow-y-auto border border-gray-300 rounded-md p-3">
                <div
                  v-for="org in organizations"
                  :key="org.id"
                  class="flex items-center"
                >
                  <input
                    :id="`org-${org.id}`"
                    v-model="form.target_organizations"
                    :value="org.id"
                    type="checkbox"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label
                    :for="`org-${org.id}`"
                    class="ml-2 text-sm text-gray-700 cursor-pointer"
                  >
                    {{ getOrgDisplayName(org) }}
                  </label>
                </div>
              </div>
            </div>

            <!-- 时间设置 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  开始时间 <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.start_time"
                  type="datetime-local"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  结束时间 <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.end_time"
                  type="datetime-local"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
            </div>

            <!-- 优先级 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                优先级
              </label>
              <select
                v-model="form.priority"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="low">低</option>
                <option value="normal">普通</option>
                <option value="high">高</option>
                <option value="urgent">紧急</option>
              </select>
            </div>

            <!-- 任务说明 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                任务说明
              </label>
              <textarea
                v-model="form.instructions"
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="请输入任务说明和填报要求"
              ></textarea>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end space-x-4">
            <router-link
              to="/statistics-tasks"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              取消
            </router-link>
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50"
            >
              {{ loading ? '创建中...' : '创建任务' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import api from '@/utils/api';

const router = useRouter();
const authStore = useAuthStore();

// 响应式数据
const loading = ref(false);
const formTemplates = ref([]);
const organizations = ref([]);

const form = ref({
  title: '',
  form_template_id: '',
  target_organizations: [],
  start_time: '',
  end_time: '',
  priority: 'normal',
  instructions: ''
});

// 方法
const handleLogout = async () => {
  await authStore.logout();
  router.push('/login');
};

const loadFormTemplates = async () => {
  try {
    const response = await api.get('/form-templates');
    formTemplates.value = response.data.data.data || [];
  } catch (error) {
    console.error('加载表单模板失败:', error);
  }
};

const loadOrganizations = async () => {
  try {
    const response = await api.get('/organizations');
    organizations.value = response.data.data.data || [];
  } catch (error) {
    console.error('加载组织列表失败:', error);
  }
};

const getOrgDisplayName = (org: any) => {
  const levelPrefix = '  '.repeat(org.level - 1);
  const typeMap: Record<string, string> = {
    county: '县/区教育局',
    district: '学区中心校',
    school: '学校',
    kindergarten: '幼儿园'
  };
  return `${levelPrefix}${org.name} (${typeMap[org.type] || org.type})`;
};

const handleSubmit = async () => {
  if (form.value.target_organizations.length === 0) {
    alert('请至少选择一个目标组织');
    return;
  }

  loading.value = true;
  try {
    await api.post('/statistics-tasks', form.value);
    router.push('/statistics-tasks');
  } catch (error: any) {
    console.error('创建任务失败:', error);
    alert('创建失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 生命周期
onMounted(() => {
  loadFormTemplates();
  loadOrganizations();
  
  // 设置默认时间
  const now = new Date();
  const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
  const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
  
  form.value.start_time = tomorrow.toISOString().slice(0, 16);
  form.value.end_time = nextWeek.toISOString().slice(0, 16);
});
</script>
