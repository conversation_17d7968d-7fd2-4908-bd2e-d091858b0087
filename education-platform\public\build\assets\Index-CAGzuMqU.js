import{d as A,u as D,r as i,m as L,g as N,c as u,a as e,i as b,j as w,k as S,t as r,h as H,b as k,e as C,v as Y,q as z,y as q,F,p as U,f as E,s as j,o as p,l as I,x as Q}from"./app-D0Qwllno.js";import{d as R}from"./dayjs.min-Cbbdfn5l.js";const G={class:"min-h-screen bg-gray-50"},J={class:"bg-white shadow"},K={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},O={class:"flex justify-between h-16"},P={class:"flex items-center space-x-4"},W={class:"flex items-center space-x-4"},X={class:"text-sm text-gray-700"},Z={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},ee={class:"px-4 py-6 sm:px-0"},te={class:"mb-6 flex justify-between items-center"},se={class:"flex space-x-4"},oe={class:"relative"},ae={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},re={class:"p-6"},ne={class:"flex items-center justify-between mb-4"},le={class:"text-lg font-medium text-gray-900"},de={class:"text-sm text-gray-600 mb-4"},ie={class:"flex items-center justify-between text-sm text-gray-500 mb-4"},ce={class:"text-xs text-gray-400 mb-4"},ue={class:"flex space-x-2"},pe=["onClick"],ge=["onClick"],xe=["onClick"],ve={key:0,class:"text-center py-12"},me={key:1,class:"mt-6 flex items-center justify-between"},_e={class:"text-sm text-gray-700"},fe={class:"flex space-x-2"},ye=["disabled"],he=["disabled"],Ce=A({__name:"Index",setup(be){const _=E(),f=D(),g=i([]),x=i(""),v=i(""),y=i(!1);i(!1);const a=i({current_page:1,last_page:1,per_page:15,total:0,from:0,to:0}),h=L(()=>{let o=g.value;if(x.value){const t=x.value.toLowerCase();o=o.filter(n=>n.name.toLowerCase().includes(t)||n.description.toLowerCase().includes(t))}return v.value&&(o=o.filter(t=>t.category===v.value)),o}),V=async()=>{await f.logout(),_.push("/login")},$=o=>R(o).format("YYYY-MM-DD HH:mm"),m=async(o=1)=>{var t,n,c;try{y.value=!0;const s=await j.get(`/form-templates?page=${o}`);if((n=(t=s.data)==null?void 0:t.data)!=null&&n.data){const l=s.data.data.data||[];g.value=(Array.isArray(l)?l:[]).map(d=>({...d,field_count:d.fields?d.fields.length:0})),a.value={current_page:s.data.data.current_page,last_page:s.data.data.last_page,per_page:s.data.data.per_page,total:s.data.data.total,from:s.data.data.from,to:s.data.data.to}}else{const l=((c=s.data)==null?void 0:c.data)||s.data||[];g.value=(Array.isArray(l)?l:[]).map(d=>({...d,field_count:d.fields?d.fields.length:0}))}}catch(s){console.error("加载表单模板失败:",s),g.value=[]}finally{y.value=!1}},M=o=>{_.push(`/form-templates/${o.id}`)},T=o=>{_.push(`/form-templates/${o.id}/edit`)},B=async o=>{try{await j.post(`/form-templates/${o.id}/duplicate`),await m()}catch(t){console.error("复制模板失败:",t)}};return N(()=>{m()}),(o,t)=>{var c;const n=S("router-link");return p(),u("div",G,[e("nav",J,[e("div",K,[e("div",O,[e("div",P,[b(n,{to:"/dashboard",class:"text-indigo-600 hover:text-indigo-500"},{default:w(()=>t[4]||(t[4]=[I(" ← 返回首页 ",-1)])),_:1,__:[4]}),t[5]||(t[5]=e("h1",{class:"text-xl font-semibold text-gray-900"}," 表单模板管理 ",-1))]),e("div",W,[e("span",X,r((c=H(f).user)==null?void 0:c.name),1),e("button",{onClick:V,class:"text-sm text-gray-500 hover:text-gray-700"}," 退出登录 ")])])])]),e("div",Z,[e("div",ee,[e("div",te,[e("div",se,[e("div",oe,[C(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>x.value=s),type:"text",placeholder:"搜索表单模板...",class:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[Y,x.value]]),t[6]||(t[6]=e("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1))]),C(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>v.value=s),class:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[7]||(t[7]=[q('<option value="">所有分类</option><option value="基础信息">基础信息</option><option value="教学统计">教学统计</option><option value="财务统计">财务统计</option><option value="人员统计">人员统计</option><option value="设施统计">设施统计</option>',6)]),512),[[z,v.value]])]),b(n,{to:"/form-templates/create",class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"},{default:w(()=>t[8]||(t[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"创建模板",-1)])),_:1,__:[8]})]),e("div",ae,[(p(!0),u(F,null,U(h.value,s=>(p(),u("div",{key:s.id,class:"bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200"},[e("div",re,[e("div",ne,[e("h3",le,r(s.name),1),e("span",{class:Q(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.is_active?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"])},r(s.is_active?"启用":"禁用"),3)]),e("p",de,r(s.description||"暂无描述"),1),e("div",ie,[e("span",null,"分类: "+r(s.category),1),e("span",null,"字段: "+r(s.field_count)+"个",1)]),e("div",ce," 创建时间: "+r($(s.created_at)),1),e("div",ue,[e("button",{onClick:l=>M(s),class:"flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded text-sm hover:bg-blue-100"}," 查看 ",8,pe),e("button",{onClick:l=>T(s),class:"flex-1 bg-gray-50 text-gray-600 px-3 py-2 rounded text-sm hover:bg-gray-100"}," 编辑 ",8,ge),e("button",{onClick:l=>B(s),class:"flex-1 bg-green-50 text-green-600 px-3 py-2 rounded text-sm hover:bg-green-100"}," 复制 ",8,xe)])])]))),128))]),h.value.length===0?(p(),u("div",ve,t[9]||(t[9]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"暂无表单模板",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"开始创建表单模板来收集数据。",-1)]))):k("",!0),a.value.total>a.value.per_page?(p(),u("div",me,[e("div",_e," 显示 "+r(a.value.from)+" 到 "+r(a.value.to)+" 条，共 "+r(a.value.total)+" 条记录 ",1),e("div",fe,[e("button",{onClick:t[2]||(t[2]=s=>m(a.value.current_page-1)),disabled:a.value.current_page<=1,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 上一页 ",8,ye),e("button",{onClick:t[3]||(t[3]=s=>m(a.value.current_page+1)),disabled:a.value.current_page>=a.value.last_page,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 下一页 ",8,he)])])):k("",!0)])])])}}});export{Ce as default};
