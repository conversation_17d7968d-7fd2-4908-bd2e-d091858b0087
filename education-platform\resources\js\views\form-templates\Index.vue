<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center space-x-4">
            <router-link to="/dashboard" class="text-indigo-600 hover:text-indigo-500">
              ← 返回首页
            </router-link>
            <h1 class="text-xl font-semibold text-gray-900">
              表单模板管理
            </h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">
              {{ authStore.user?.name }}
            </span>
            <button
              @click="handleLogout"
              class="text-sm text-gray-500 hover:text-gray-700"
            >
              退出登录
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- 操作栏 -->
        <div class="mb-6 flex justify-between items-center">
          <div class="flex space-x-4">
            <!-- 搜索框 -->
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索表单模板..."
                class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>

            <!-- 分类筛选 -->
            <select
              v-model="selectedCategory"
              class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">所有分类</option>
              <option value="基础信息">基础信息</option>
              <option value="教学统计">教学统计</option>
              <option value="财务统计">财务统计</option>
              <option value="人员统计">人员统计</option>
              <option value="设施统计">设施统计</option>
            </select>
          </div>

          <!-- 创建模板按钮 -->
          <router-link
            to="/form-templates/create"
            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span>创建模板</span>
          </router-link>
        </div>

        <!-- 模板列表 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            class="bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200"
          >
            <div class="p-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">{{ template.name }}</h3>
                <span
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    template.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  ]"
                >
                  {{ template.is_active ? '启用' : '禁用' }}
                </span>
              </div>

              <p class="text-sm text-gray-600 mb-4">{{ template.description || '暂无描述' }}</p>

              <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span>分类: {{ template.category }}</span>
                <span>字段: {{ template.field_count }}个</span>
              </div>

              <div class="text-xs text-gray-400 mb-4">
                创建时间: {{ formatDate(template.created_at) }}
              </div>

              <div class="flex space-x-2">
                <button
                  @click="viewTemplate(template)"
                  class="flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded text-sm hover:bg-blue-100"
                >
                  查看
                </button>
                <button
                  @click="editTemplate(template)"
                  class="flex-1 bg-gray-50 text-gray-600 px-3 py-2 rounded text-sm hover:bg-gray-100"
                >
                  编辑
                </button>
                <button
                  @click="duplicateTemplate(template)"
                  class="flex-1 bg-green-50 text-green-600 px-3 py-2 rounded text-sm hover:bg-green-100"
                >
                  复制
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredTemplates.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">暂无表单模板</h3>
          <p class="mt-1 text-sm text-gray-500">开始创建表单模板来收集数据。</p>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.total > pagination.per_page" class="mt-6 flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示 {{ pagination.from }} 到 {{ pagination.to }} 条，共 {{ pagination.total }} 条记录
          </div>
          <div class="flex space-x-2">
            <button
              @click="loadTemplates(pagination.current_page - 1)"
              :disabled="pagination.current_page <= 1"
              class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <button
              @click="loadTemplates(pagination.current_page + 1)"
              :disabled="pagination.current_page >= pagination.last_page"
              class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import api from '@/utils/api';
import dayjs from 'dayjs';

interface FormTemplate {
  id: number;
  name: string;
  description: string;
  category: string;
  is_active: boolean;
  field_count: number;
  created_at: string;
  fields: FormField[];
}

interface FormField {
  id: string;
  type: string;
  label: string;
  required: boolean;
  options?: string[];
  validation?: any;
}

const router = useRouter();
const authStore = useAuthStore();

// 响应式数据
const templates = ref<FormTemplate[]>([]);
const searchQuery = ref('');
const selectedCategory = ref('');
const loading = ref(false);
const showCreateModal = ref(false);

// 分页数据
const pagination = ref({
  current_page: 1,
  last_page: 1,
  per_page: 15,
  total: 0,
  from: 0,
  to: 0,
});

// 计算属性
const filteredTemplates = computed(() => {
  let filtered = templates.value;

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(template =>
      template.name.toLowerCase().includes(query) ||
      template.description.toLowerCase().includes(query)
    );
  }

  if (selectedCategory.value) {
    filtered = filtered.filter(template =>
      template.category === selectedCategory.value
    );
  }

  return filtered;
});

// 方法
const handleLogout = async () => {
  await authStore.logout();
  router.push('/login');
};

const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm');
};

const loadTemplates = async (page = 1) => {
  try {
    loading.value = true;
    const response = await api.get(`/form-templates?page=${page}`);

    // 处理分页数据
    if (response.data?.data?.data) {
      // 分页数据结构
      const data = response.data.data.data || [];
      templates.value = (Array.isArray(data) ? data : []).map((template: any) => ({
        ...template,
        field_count: template.fields ? template.fields.length : 0,
      }));

      pagination.value = {
        current_page: response.data.data.current_page,
        last_page: response.data.data.last_page,
        per_page: response.data.data.per_page,
        total: response.data.data.total,
        from: response.data.data.from,
        to: response.data.data.to,
      };
    } else {
      // 直接数组结构
      const data = response.data?.data || response.data || [];
      templates.value = (Array.isArray(data) ? data : []).map((template: any) => ({
        ...template,
        field_count: template.fields ? template.fields.length : 0,
      }));
    }
  } catch (error) {
    console.error('加载表单模板失败:', error);
    templates.value = [];
  } finally {
    loading.value = false;
  }
};

const viewTemplate = (template: FormTemplate) => {
  router.push(`/form-templates/${template.id}`);
};

const editTemplate = (template: FormTemplate) => {
  router.push(`/form-templates/${template.id}/edit`);
};

const duplicateTemplate = async (template: FormTemplate) => {
  try {
    await api.post(`/form-templates/${template.id}/duplicate`);
    await loadTemplates();
  } catch (error) {
    console.error('复制模板失败:', error);
  }
};

// 生命周期
onMounted(() => {
  loadTemplates();
});
</script>
