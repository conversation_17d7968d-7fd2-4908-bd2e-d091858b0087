<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as DB;

// 配置数据库连接
$capsule = new DB;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '127.0.0.1',
    'database' => 'education_platform',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

// 定义正确的层级映射
$typeToLevel = [
    'county' => 1,      // 县/区教育局
    'district' => 2,    // 学区中心校
    'school' => 3,      // 学校
    'kindergarten' => 3 // 幼儿园
];

echo "开始修复组织层级...\n";

// 获取所有组织
$organizations = DB::table('organizations')->get();

foreach ($organizations as $org) {
    $correctLevel = $typeToLevel[$org->type] ?? 1;
    
    if ($org->level != $correctLevel) {
        echo "修复组织: {$org->name} (类型: {$org->type}) 从层级 {$org->level} 改为 {$correctLevel}\n";
        
        DB::table('organizations')
            ->where('id', $org->id)
            ->update(['level' => $correctLevel]);
    }
}

echo "组织层级修复完成！\n";

// 显示修复后的结果
echo "\n修复后的组织结构:\n";
$organizations = DB::table('organizations')->orderBy('level')->orderBy('sort_order')->get();

foreach ($organizations as $org) {
    $indent = str_repeat('  ', $org->level - 1);
    echo "{$indent}[L{$org->level}] {$org->name} ({$org->type})\n";
}
