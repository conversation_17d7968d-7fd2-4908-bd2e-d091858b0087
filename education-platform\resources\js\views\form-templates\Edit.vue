<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <svg class="animate-spin h-8 w-8 text-blue-600 mx-auto" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="mt-2 text-sm text-gray-600">加载中...</p>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 导航栏 -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center space-x-4">
              <router-link to="/form-templates" class="text-indigo-600 hover:text-indigo-500">
                ← 返回模板列表
              </router-link>
              <h1 class="text-xl font-semibold text-gray-900">
                编辑表单模板 - {{ template.name }}
              </h1>
            </div>
            <div class="flex items-center space-x-4">
              <button
                @click="saveTemplate"
                :disabled="saving"
                class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
              >
                <svg v-if="saving" class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>{{ saving ? '保存中...' : '保存更改' }}</span>
              </button>
              <button
                @click="previewTemplate"
                class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <span>预览</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="flex gap-6">
          <!-- 左侧：表单基本信息 -->
          <div class="w-1/4 space-y-6">
            <!-- 基本信息卡片 -->
            <div class="bg-white rounded-lg shadow p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">模板名称</label>
                  <input
                    v-model="template.name"
                    type="text"
                    placeholder="请输入模板名称"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">分类</label>
                  <select
                    v-model="template.category"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">请选择分类</option>
                    <option value="基础信息">基础信息</option>
                    <option value="教学统计">教学统计</option>
                    <option value="财务统计">财务统计</option>
                    <option value="人员统计">人员统计</option>
                    <option value="设施统计">设施统计</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                  <textarea
                    v-model="template.description"
                    rows="3"
                    placeholder="请输入模板描述"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  ></textarea>
                </div>
              </div>
            </div>

            <!-- 字段组件库 -->
            <div class="bg-white rounded-lg shadow p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">字段组件</h3>
              <div class="space-y-2">
                <div
                  v-for="fieldType in fieldTypes"
                  :key="fieldType.type"
                  @click="addField(fieldType)"
                  class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
                >
                  <div class="flex-shrink-0 mr-3">
                    <svg class="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path v-if="fieldType.type === 'text'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
                      <path v-else-if="fieldType.type === 'textarea'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                      <path v-else-if="fieldType.type === 'number'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                      <path v-else-if="fieldType.type === 'select'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                      <path v-else-if="fieldType.type === 'checkbox'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      <path v-else-if="fieldType.type === 'radio'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      <path v-else-if="fieldType.type === 'date'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      <path v-else-if="fieldType.type === 'file'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
                    </svg>
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ fieldType.label }}</div>
                    <div class="text-xs text-gray-500">{{ fieldType.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 中间：表单设计区域 -->
          <div class="flex-1">
            <div class="bg-white rounded-lg shadow">
              <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">表单设计</h3>
                <p class="text-sm text-gray-500 mt-1">拖拽左侧组件到此区域，或点击组件直接添加</p>
              </div>
              
              <!-- 表单预览区域 -->
              <div class="p-6 min-h-96">
                <div v-if="template.config.fields.length === 0" class="text-center py-12">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-900">暂无字段</h3>
                  <p class="mt-1 text-sm text-gray-500">从左侧选择字段组件开始设计表单</p>
                </div>

                <!-- 字段列表 -->
                <div v-else class="space-y-4">
                  <FormFieldEditor
                    v-for="(field, index) in template.config.fields"
                    :key="field.id"
                    :field="field"
                    :index="index"
                    :total-fields="template.config.fields.length"
                    :selected="selectedField?.id === field.id"
                    @update="updateField"
                    @delete="deleteField"
                    @move-up="moveFieldUp"
                    @move-down="moveFieldDown"
                    @select="selectField"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：字段属性编辑器 -->
          <div class="w-1/4">
            <div class="bg-white rounded-lg shadow p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">字段属性</h3>
              
              <div v-if="selectedField">
                <FieldPropertyEditor
                  :field="selectedField"
                  @update="updateSelectedField"
                />
              </div>
              
              <div v-else class="text-center py-8">
                <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
                <p class="mt-2 text-sm text-gray-500">选择字段以编辑属性</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预览模态框 -->
      <FormPreviewModal
        v-if="showPreview"
        :template="template"
        @close="showPreview = false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import api from '@/utils/api';
import FormFieldEditor from '@/components/form-builder/FormFieldEditor.vue';
import FieldPropertyEditor from '@/components/form-builder/FieldPropertyEditor.vue';
import FormPreviewModal from '@/components/form-builder/FormPreviewModal.vue';

// 字段类型定义
interface FormField {
  id: string;
  type: string;
  label: string;
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: any;
  description?: string;
}

interface FormTemplate {
  id?: number;
  name: string;
  category: string;
  description: string;
  config: {
    fields: FormField[];
  };
}

const router = useRouter();
const route = useRoute();

// 响应式数据
const template = reactive<FormTemplate>({
  name: '',
  category: '',
  description: '',
  config: {
    fields: []
  }
});

const selectedField = ref<FormField | null>(null);
const loading = ref(true);
const saving = ref(false);
const showPreview = ref(false);

// 字段类型配置
const fieldTypes = [
  {
    type: 'text',
    label: '单行文本',
    description: '输入单行文本内容'
  },
  {
    type: 'textarea',
    label: '多行文本',
    description: '输入多行文本内容'
  },
  {
    type: 'number',
    label: '数字',
    description: '输入数字'
  },
  {
    type: 'select',
    label: '下拉选择',
    description: '从选项中选择一个'
  },
  {
    type: 'checkbox',
    label: '多选框',
    description: '选择多个选项'
  },
  {
    type: 'radio',
    label: '单选框',
    description: '选择一个选项'
  },
  {
    type: 'date',
    label: '日期',
    description: '选择日期'
  },
  {
    type: 'file',
    label: '文件上传',
    description: '上传文件'
  }
];

// 生命周期
onMounted(async () => {
  await loadTemplate();
});

// 方法
const loadTemplate = async () => {
  try {
    loading.value = true;
    const templateId = route.params.id;
    const response = await api.get(`/api/v1/form-templates/${templateId}`);
    const data = response.data.data;
    
    // 更新模板数据
    Object.assign(template, {
      id: data.id,
      name: data.name,
      category: data.category,
      description: data.description,
      config: data.config || { fields: [] }
    });
  } catch (error) {
    console.error('加载模板失败:', error);
    alert('加载模板失败，请重试');
    router.push('/form-templates');
  } finally {
    loading.value = false;
  }
};

const addField = (fieldType: any) => {
  const newField: FormField = {
    id: `field_${Date.now()}`,
    type: fieldType.type,
    label: fieldType.label,
    required: false,
    placeholder: `请输入${fieldType.label}`,
  };

  // 为选择类型字段添加默认选项
  if (['select', 'checkbox', 'radio'].includes(fieldType.type)) {
    newField.options = ['选项1', '选项2', '选项3'];
  }

  template.config.fields.push(newField);
  selectedField.value = newField;
};

const updateField = (index: number, updatedField: FormField) => {
  template.config.fields[index] = updatedField;
  if (selectedField.value?.id === updatedField.id) {
    selectedField.value = updatedField;
  }
};

const deleteField = (index: number) => {
  const deletedField = template.config.fields[index];
  template.config.fields.splice(index, 1);
  if (selectedField.value?.id === deletedField.id) {
    selectedField.value = null;
  }
};

const moveFieldUp = (index: number) => {
  if (index > 0) {
    const field = template.config.fields.splice(index, 1)[0];
    template.config.fields.splice(index - 1, 0, field);
  }
};

const moveFieldDown = (index: number) => {
  if (index < template.config.fields.length - 1) {
    const field = template.config.fields.splice(index, 1)[0];
    template.config.fields.splice(index + 1, 0, field);
  }
};

const selectField = (field: FormField) => {
  selectedField.value = field;
};

const updateSelectedField = (updatedField: FormField) => {
  const index = template.config.fields.findIndex(f => f.id === updatedField.id);
  if (index !== -1) {
    updateField(index, updatedField);
  }
};

const saveTemplate = async () => {
  if (!template.name || !template.category) {
    alert('请填写模板名称和分类');
    return;
  }

  if (template.config.fields.length === 0) {
    alert('请至少添加一个字段');
    return;
  }

  try {
    saving.value = true;
    await api.put(`/api/v1/form-templates/${template.id}`, {
      name: template.name,
      category: template.category,
      description: template.description,
      config: template.config
    });
    alert('模板更新成功！');
    router.push('/form-templates');
  } catch (error) {
    console.error('保存模板失败:', error);
    alert('保存模板失败，请重试');
  } finally {
    saving.value = false;
  }
};

const previewTemplate = () => {
  showPreview.value = true;
};
</script>
