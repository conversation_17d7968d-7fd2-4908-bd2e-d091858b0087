<template>
  <div 
    class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors"
    :class="{ 'border-blue-500 bg-blue-50': isSelected }"
    @click="selectField"
  >
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center space-x-2">
        <svg class="h-4 w-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path v-if="field.type === 'text'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
          <path v-else-if="field.type === 'textarea'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
          <path v-else-if="field.type === 'number'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
          <path v-else-if="field.type === 'select'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
          <path v-else-if="field.type === 'checkbox'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          <path v-else-if="field.type === 'radio'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          <path v-else-if="field.type === 'date'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          <path v-else-if="field.type === 'file'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
        </svg>
        <span class="text-sm font-medium text-gray-900">{{ field.label }}</span>
        <span v-if="field.required" class="text-red-500 text-xs">*</span>
      </div>
      
      <div class="flex items-center space-x-1">
        <!-- 上移按钮 -->
        <button
          @click.stop="$emit('moveUp', index)"
          :disabled="index === 0"
          class="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
          title="上移"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
          </svg>
        </button>
        
        <!-- 下移按钮 -->
        <button
          @click.stop="$emit('moveDown', index)"
          :disabled="index === totalFields - 1"
          class="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
          title="下移"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        <!-- 删除按钮 -->
        <button
          @click.stop="deleteField"
          class="p-1 text-red-400 hover:text-red-600"
          title="删除"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    </div>

    <!-- 字段预览 -->
    <div class="space-y-2">
      <!-- 文本输入 -->
      <div v-if="field.type === 'text'">
        <input
          type="text"
          :placeholder="field.placeholder"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled
        />
      </div>

      <!-- 多行文本 -->
      <div v-else-if="field.type === 'textarea'">
        <textarea
          :placeholder="field.placeholder"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled
        ></textarea>
      </div>

      <!-- 数字输入 -->
      <div v-else-if="field.type === 'number'">
        <input
          type="number"
          :placeholder="field.placeholder"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled
        />
      </div>

      <!-- 下拉选择 -->
      <div v-else-if="field.type === 'select'">
        <select
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled
        >
          <option value="">请选择</option>
          <option v-for="option in field.options" :key="option" :value="option">
            {{ option }}
          </option>
        </select>
      </div>

      <!-- 多选框 -->
      <div v-else-if="field.type === 'checkbox'" class="space-y-2">
        <div v-for="option in field.options" :key="option" class="flex items-center">
          <input
            type="checkbox"
            :id="`${field.id}_${option}`"
            class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            disabled
          />
          <label :for="`${field.id}_${option}`" class="ml-2 text-sm text-gray-700">
            {{ option }}
          </label>
        </div>
      </div>

      <!-- 单选框 -->
      <div v-else-if="field.type === 'radio'" class="space-y-2">
        <div v-for="option in field.options" :key="option" class="flex items-center">
          <input
            type="radio"
            :name="field.id"
            :id="`${field.id}_${option}`"
            class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
            disabled
          />
          <label :for="`${field.id}_${option}`" class="ml-2 text-sm text-gray-700">
            {{ option }}
          </label>
        </div>
      </div>

      <!-- 日期选择 -->
      <div v-else-if="field.type === 'date'">
        <input
          type="date"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled
        />
      </div>

      <!-- 文件上传 -->
      <div v-else-if="field.type === 'file'">
        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
          <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          <p class="mt-2 text-sm text-gray-500">点击上传文件或拖拽文件到此处</p>
        </div>
      </div>

      <!-- 字段描述 -->
      <div v-if="field.description" class="text-xs text-gray-500 mt-1">
        {{ field.description }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface FormField {
  id: string;
  type: string;
  label: string;
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: any;
  description?: string;
}

interface Props {
  field: FormField;
  index: number;
  totalFields?: number;
  selected?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  totalFields: 0,
  selected: false
});

const emit = defineEmits<{
  update: [index: number, field: FormField];
  delete: [index: number];
  moveUp: [index: number];
  moveDown: [index: number];
  select: [field: FormField];
}>();

const isSelected = computed(() => props.selected);

const selectField = () => {
  emit('select', props.field);
};

const deleteField = () => {
  if (confirm('确定要删除这个字段吗？')) {
    emit('delete', props.index);
  }
};


</script>

<style scoped>
/* 自定义样式 */
.field-editor {
  transition: all 0.2s ease-in-out;
}

.field-editor:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
