import{d as te,u as se,r as i,m as oe,g as ae,c as d,a as e,b as h,i as le,j as re,k as ne,t as r,h as ie,e as g,v as U,q as H,y as de,F as M,p as j,w as ue,l as V,f as ce,s as m,o as u,x as N,z as pe}from"./app-D0Qwllno.js";import{d as me}from"./dayjs.min-Cbbdfn5l.js";const xe={class:"min-h-screen bg-gray-50"},ve={class:"bg-white shadow"},ge={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ye={class:"flex justify-between h-16"},fe={class:"flex items-center space-x-4"},be={class:"flex items-center space-x-4"},he={class:"text-sm text-gray-700"},we={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},_e={class:"px-4 py-6 sm:px-0"},ke={class:"mb-6 flex justify-between items-center"},Ce={class:"flex space-x-4"},ze={class:"relative"},Ue={class:"flex space-x-3"},Me={class:"bg-white shadow rounded-lg overflow-hidden"},je={class:"min-w-full divide-y divide-gray-200"},Ve={class:"bg-white divide-y divide-gray-200"},$e={class:"px-6 py-4 whitespace-nowrap"},De={class:"flex items-center"},Fe={class:"flex-shrink-0 h-10 w-10"},Be={class:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center"},Se={class:"text-sm font-medium text-gray-700"},Le={class:"ml-4"},Ee={class:"text-sm font-medium text-gray-900"},qe={class:"text-sm text-gray-500"},Ae={class:"px-6 py-4 whitespace-nowrap"},He={class:"text-sm text-gray-900"},Ne={class:"text-sm text-gray-500"},Re={class:"px-6 py-4 whitespace-nowrap"},Ie={class:"flex flex-wrap gap-1"},Te={class:"px-6 py-4 whitespace-nowrap"},Ye={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Oe={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Pe={class:"flex space-x-2"},Qe=["onClick"],Ge=["onClick"],Je=["onClick"],Ke={key:0,class:"text-center py-12"},We={key:0,class:"mt-6 flex items-center justify-between"},Xe={class:"text-sm text-gray-700"},Ze={class:"flex space-x-2"},et=["disabled"],tt=["disabled"],st={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},ot={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},at={class:"mt-3"},lt={class:"text-lg font-medium text-gray-900 mb-4"},rt={class:"space-y-4"},nt={key:0},it=["value"],dt={class:"mt-2 space-y-2"},ut=["value"],ct={class:"ml-2 text-sm text-gray-700"},pt={class:"mt-6 flex justify-end space-x-3"},mt=["disabled"],xt={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},vt={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},gt={class:"mt-3"},yt={class:"mb-4"},ft={class:"flex justify-end space-x-3"},bt=["disabled"],kt=te({__name:"Index",setup(ht){const R=ce(),F=se(),B=i([]),S=i([]),L=i([]),w=i(""),_=i(""),p=i(!1),x=i(!1),k=i(!1),$=i(!1),C=i(null),f=i(null),z=i(!1),c=i({current_page:1,last_page:1,per_page:15,total:0,from:0,to:0}),n=i({name:"",email:"",password:"",organization_id:"",role_ids:[]}),E=oe(()=>{let o=B.value||[];if(w.value){const t=w.value.toLowerCase();o=o.filter(l=>{var a,s;return((a=l.name)==null?void 0:a.toLowerCase().includes(t))||((s=l.email)==null?void 0:s.toLowerCase().includes(t))})}return _.value&&(o=o.filter(t=>{var l;return(l=t.roles)==null?void 0:l.some(a=>a.display_name===_.value)})),o}),I=async()=>{await F.logout(),R.push("/login")},T=o=>me(o).format("YYYY-MM-DD HH:mm"),v=async(o=1)=>{try{p.value=!0;const t=await m.get(`/users?page=${o}`);B.value=t.data.data,c.value={current_page:t.data.current_page,last_page:t.data.last_page,per_page:t.data.per_page,total:t.data.total,from:t.data.from,to:t.data.to}}catch(t){console.error("加载用户列表失败:",t)}finally{p.value=!1}},Y=async()=>{try{const o=await m.get("/organizations");S.value=o.data.data}catch(o){console.error("加载机构列表失败:",o)}},O=async()=>{try{const o=await m.get("/roles");L.value=o.data.data}catch(o){console.error("加载角色列表失败:",o)}},P=async()=>{try{p.value=!0,await m.post("/users",n.value),x.value=!1,D(),await v()}catch(o){console.error("创建用户失败:",o)}finally{p.value=!1}},Q=o=>{var t,l;C.value=o,n.value={name:o.name,email:o.email,password:"",organization_id:((l=(t=o.organization)==null?void 0:t.id)==null?void 0:l.toString())||"",role_ids:o.roles.map(a=>a.id)},k.value=!0},G=async()=>{if(C.value)try{p.value=!0;const o={...n.value};delete o.password,await m.put(`/users/${C.value.id}`,o),k.value=!1,D(),await v()}catch(o){console.error("更新用户失败:",o)}finally{p.value=!1}},J=async o=>{try{await m.patch(`/api/v1/users/${o.id}/toggle-status`),await v()}catch(t){console.error("切换用户状态失败:",t)}},K=async o=>{if(confirm(`确定要重置用户 ${o.name} 的密码吗？`))try{const t=await m.post(`/users/${o.id}/reset-password`);alert(`密码已重置为: ${t.data.password}`)}catch(t){console.error("重置密码失败:",t)}},W=()=>{x.value=!1,k.value=!1,D()},D=()=>{n.value={name:"",email:"",password:"",organization_id:"",role_ids:[]},C.value=null},X=()=>{const t=[["姓名","邮箱","密码","组织编码","角色"],["张三","<EMAIL>","123456","130182003","school_admin"],["李四","<EMAIL>","123456","130182004","data_entry"]].map(y=>y.join(",")).join(`
`),l=new Blob(["\uFEFF"+t],{type:"text/csv;charset=utf-8;"}),a=document.createElement("a"),s=URL.createObjectURL(l);a.setAttribute("href",s),a.setAttribute("download","用户导入模板.csv"),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a)},Z=o=>{const t=o.target;t.files&&t.files.length>0&&(f.value=t.files[0])},q=()=>{$.value=!1,f.value=null},ee=async()=>{var o,t;if(f.value){z.value=!0;try{const l=new FormData;l.append("file",f.value);const a=await m.post("/users/import",l,{headers:{"Content-Type":"multipart/form-data"}});a.data.success?(alert(`导入成功！共导入 ${a.data.imported_count} 个用户`),await v(),q()):alert("导入失败："+(a.data.message||"未知错误"))}catch(l){console.error("导入失败:",l),alert("导入失败："+(((t=(o=l.response)==null?void 0:o.data)==null?void 0:t.message)||l.message||"网络错误"))}finally{z.value=!1}}};return ae(async()=>{await Promise.all([v(),Y(),O()])}),(o,t)=>{var a;const l=ne("router-link");return u(),d("div",xe,[e("nav",ve,[e("div",ge,[e("div",ye,[e("div",fe,[le(l,{to:"/dashboard",class:"text-indigo-600 hover:text-indigo-500"},{default:re(()=>t[12]||(t[12]=[V(" ← 返回首页 ",-1)])),_:1,__:[12]}),t[13]||(t[13]=e("h1",{class:"text-xl font-semibold text-gray-900"}," 用户管理 ",-1))]),e("div",be,[e("span",he,r((a=ie(F).user)==null?void 0:a.name),1),e("button",{onClick:I,class:"text-sm text-gray-500 hover:text-gray-700"}," 退出登录 ")])])])]),e("div",we,[e("div",_e,[e("div",ke,[e("div",Ce,[e("div",ze,[g(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>w.value=s),type:"text",placeholder:"搜索用户...",class:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[U,w.value]]),t[14]||(t[14]=e("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1))]),g(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>_.value=s),class:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[15]||(t[15]=[de('<option value="">所有角色</option><option value="超级管理员">超级管理员</option><option value="系统管理员">系统管理员</option><option value="股室管理员">股室管理员</option><option value="学区管理员">学区管理员</option><option value="学校管理员">学校管理员</option><option value="填报员">填报员</option>',7)]),512),[[H,_.value]])]),e("div",Ue,[e("button",{onClick:X,class:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"},t[16]||(t[16]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("span",null,"下载模板",-1)])),e("button",{onClick:t[2]||(t[2]=s=>$.value=!0),class:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center space-x-2"},t[17]||(t[17]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"})],-1),e("span",null,"批量导入",-1)])),e("button",{onClick:t[3]||(t[3]=s=>x.value=!0),class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"},t[18]||(t[18]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"添加用户",-1)]))])]),e("div",Me,[e("table",je,[t[19]||(t[19]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"用户信息"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"所属机构"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"角色"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"状态"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"创建时间"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"操作")])],-1)),e("tbody",Ve,[(u(!0),d(M,null,j(E.value,s=>{var y,A;return u(),d("tr",{key:s.id,class:"hover:bg-gray-50"},[e("td",$e,[e("div",De,[e("div",Fe,[e("div",Be,[e("span",Se,r(s.name.charAt(0)),1)])]),e("div",Le,[e("div",Ee,r(s.name),1),e("div",qe,r(s.email),1)])])]),e("td",Ae,[e("div",He,r(((y=s.organization)==null?void 0:y.name)||"-"),1),e("div",Ne,r(((A=s.organization)==null?void 0:A.type)||"-"),1)]),e("td",Re,[e("div",Ie,[(u(!0),d(M,null,j(s.roles,b=>(u(),d("span",{key:b.id,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},r(b.display_name),1))),128))])]),e("td",Te,[e("span",{class:N(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},r(s.status?"启用":"禁用"),3)]),e("td",Ye,r(T(s.created_at)),1),e("td",Oe,[e("div",Pe,[e("button",{onClick:b=>Q(s),class:"text-blue-600 hover:text-blue-900"}," 编辑 ",8,Qe),e("button",{onClick:b=>J(s),class:N(["hover:text-gray-900",s.status?"text-red-600":"text-green-600"])},r(s.status?"禁用":"启用"),11,Ge),e("button",{onClick:b=>K(s),class:"text-purple-600 hover:text-purple-900"}," 重置密码 ",8,Je)])])])}),128))])]),E.value.length===0?(u(),d("div",Ke,t[20]||(t[20]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"暂无用户",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"开始添加用户来管理系统访问权限。",-1)]))):h("",!0)]),c.value.total>c.value.per_page?(u(),d("div",We,[e("div",Xe," 显示 "+r(c.value.from)+" 到 "+r(c.value.to)+" 条，共 "+r(c.value.total)+" 条记录 ",1),e("div",Ze,[e("button",{onClick:t[4]||(t[4]=s=>v(c.value.current_page-1)),disabled:c.value.current_page<=1,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 上一页 ",8,et),e("button",{onClick:t[5]||(t[5]=s=>v(c.value.current_page+1)),disabled:c.value.current_page>=c.value.last_page,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 下一页 ",8,tt)])])):h("",!0)])]),x.value||k.value?(u(),d("div",st,[e("div",ot,[e("div",at,[e("h3",lt,r(x.value?"添加用户":"编辑用户"),1),e("form",{onSubmit:t[11]||(t[11]=ue(s=>x.value?P():G(),["prevent"]))},[e("div",rt,[e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700"},"姓名",-1)),g(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>n.value.name=s),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[U,n.value.name]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700"},"邮箱",-1)),g(e("input",{"onUpdate:modelValue":t[7]||(t[7]=s=>n.value.email=s),type:"email",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[U,n.value.email]])]),x.value?(u(),d("div",nt,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700"},"密码",-1)),g(e("input",{"onUpdate:modelValue":t[8]||(t[8]=s=>n.value.password=s),type:"password",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[U,n.value.password]])])):h("",!0),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700"},"所属机构",-1)),g(e("select",{"onUpdate:modelValue":t[9]||(t[9]=s=>n.value.organization_id=s),class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"},[t[24]||(t[24]=e("option",{value:""},"请选择机构",-1)),(u(!0),d(M,null,j(S.value,s=>(u(),d("option",{key:s.id,value:s.id},r(s.name),9,it))),128))],512),[[H,n.value.organization_id]])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700"},"角色",-1)),e("div",dt,[(u(!0),d(M,null,j(L.value,s=>(u(),d("label",{key:s.id,class:"flex items-center"},[g(e("input",{"onUpdate:modelValue":t[10]||(t[10]=y=>n.value.role_ids=y),value:s.id,type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,8,ut),[[pe,n.value.role_ids]]),e("span",ct,r(s.display_name),1)]))),128))])])]),e("div",pt,[e("button",{type:"button",onClick:W,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"}," 取消 "),e("button",{type:"submit",disabled:p.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"},r(p.value?"保存中...":"保存"),9,mt)])],32)])])])):h("",!0),$.value?(u(),d("div",xt,[e("div",vt,[e("div",gt,[t[28]||(t[28]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"批量导入用户",-1)),e("div",yt,[t[27]||(t[27]=e("p",{class:"text-sm text-gray-600 mb-2"},"请选择Excel文件进行批量导入：",-1)),e("input",{type:"file",accept:".xlsx,.xls",onChange:Z,class:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"},null,32)]),t[29]||(t[29]=e("div",{class:"mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md"},[e("p",{class:"text-sm text-yellow-800"},[e("strong",null,"导入说明："),e("br"),V(" • Excel文件应包含：姓名、邮箱、密码、组织编码、角色列"),e("br"),V(" • 角色可选：super_admin, system_admin, department_admin, district_admin, school_admin, data_entry"),e("br"),V(" • 组织编码必须是已存在的组织编码 ")])],-1)),e("div",ft,[e("button",{onClick:q,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"}," 取消 "),e("button",{onClick:ee,disabled:!f.value||z.value,class:"px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 rounded-md"},r(z.value?"导入中...":"开始导入"),9,bt)])])])])):h("",!0)])}}});export{kt as default};
