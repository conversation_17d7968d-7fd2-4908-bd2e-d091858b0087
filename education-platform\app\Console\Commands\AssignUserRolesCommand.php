<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class AssignUserRolesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assign:user-roles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '为现有用户分配角色';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('为现有用户分配角色...');

        // 用户角色映射
        $userRoleMapping = [
            '<EMAIL>' => '超级管理员',
            '<EMAIL>' => '系统管理员',
            '<EMAIL>' => '股室管理员',
            '<EMAIL>' => '学区管理员',
            '<EMAIL>' => '学区管理员',
            '<EMAIL>' => '学校管理员',
            '<EMAIL>' => '学校管理员',
            '<EMAIL>' => '填报员',
            '<EMAIL>' => '填报员',
        ];

        foreach ($userRoleMapping as $email => $roleName) {
            $user = User::where('email', $email)->first();
            if ($user) {
                // 清除现有角色
                $user->syncRoles([]);
                // 分配新角色
                $user->assignRole($roleName);
                $this->info("为用户 {$user->name} ({$email}) 分配角色: {$roleName}");
            } else {
                $this->warn("未找到用户: {$email}");
            }
        }

        $this->info('角色分配完成！');

        return 0;
    }
}
