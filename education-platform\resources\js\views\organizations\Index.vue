<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center space-x-4">
            <router-link to="/dashboard" class="text-indigo-600 hover:text-indigo-500">
              ← 返回首页
            </router-link>
            <h1 class="text-xl font-semibold text-gray-900">
              组织架构管理
            </h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">
              {{ authStore.user?.name }}
            </span>
            <button
              @click="handleLogout"
              class="text-sm text-gray-500 hover:text-gray-700"
            >
              退出登录
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- 操作栏 -->
        <div class="mb-6 flex justify-between items-center">
          <div class="flex space-x-4">
            <button
              @click="showCreateModal = true"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              新增组织
            </button>
            <button
              @click="downloadTemplate"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              下载模板
            </button>
            <button
              @click="showImportModal = true"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              批量导入
            </button>
            <button
              @click="loadOrganizations"
              :disabled="loading"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              刷新
            </button>
          </div>
          <div class="flex items-center space-x-2">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索组织..."
              class="block w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
        </div>

        <!-- 组织列表 -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
          <div v-if="loading" class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <p class="mt-2 text-sm text-gray-500">加载中...</p>
          </div>
          
          <ul v-else-if="filteredOrganizations.length > 0" class="divide-y divide-gray-200">
            <li v-for="org in filteredOrganizations" :key="org.id" class="px-6 py-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                      <span class="text-sm font-medium text-gray-700">
                        {{ org.name.charAt(0) }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="flex items-center">
                      <p class="text-sm font-medium text-gray-900">
                        {{ org.name }}
                      </p>
                      <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                            :class="getTypeClass(org.type)">
                        {{ getTypeLabel(org.type) }}
                      </span>
                    </div>
                    <p class="text-sm text-gray-500">
                      编码: {{ org.code }} | 层级: {{ org.level }}
                    </p>
                    <p v-if="org.parent" class="text-xs text-gray-400">
                      上级: {{ org.parent.name }}
                    </p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="org.status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                    {{ org.status ? '启用' : '禁用' }}
                  </span>
                  <button
                    @click="editOrganization(org)"
                    class="text-indigo-600 hover:text-indigo-500 text-sm"
                  >
                    编辑
                  </button>
                  <button
                    @click="deleteOrganization(org)"
                    class="text-red-600 hover:text-red-500 text-sm"
                  >
                    删除
                  </button>
                </div>
              </div>
            </li>
          </ul>
          
          <div v-else class="p-6 text-center">
            <p class="text-sm text-gray-500">暂无组织数据</p>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.total > pagination.per_page" class="mt-6 flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示 {{ pagination.from }} 到 {{ pagination.to }} 条，共 {{ pagination.total }} 条记录
          </div>
          <div class="flex space-x-2">
            <button
              @click="loadOrganizations(pagination.current_page - 1)"
              :disabled="pagination.current_page <= 1"
              class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <button
              @click="loadOrganizations(pagination.current_page + 1)"
              :disabled="pagination.current_page >= pagination.last_page"
              class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑模态框 -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ showCreateModal ? '新增组织' : '编辑组织' }}
          </h3>
          <form @submit.prevent="submitForm">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">组织名称</label>
                <input
                  v-model="form.name"
                  type="text"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">组织编码</label>
                <div class="flex gap-2">
                  <input
                    v-model="form.code"
                    type="text"
                    required
                    placeholder="例如：SCHOOL_004, EDU_DISTRICT_003"
                    class="flex-1 mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                  <button
                    type="button"
                    @click="generateCode"
                    class="mt-1 px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    生成
                  </button>
                </div>
                <p class="mt-1 text-xs text-gray-500">
                  组织编码必须唯一，建议格式：学校用SCHOOL_xxx，学区用EDU_DISTRICT_xxx，幼儿园用KINDERGARTEN_xxx
                </p>
                <div class="mt-2 text-xs text-gray-400">
                  <details>
                    <summary class="cursor-pointer hover:text-gray-600">查看已使用的编码</summary>
                    <div class="mt-1 max-h-20 overflow-y-auto bg-gray-50 p-2 rounded">
                      <div v-for="org in (organizations || [])" :key="org?.id || Math.random()" class="text-xs">
                        {{ org?.code }} - {{ org?.name }}
                      </div>
                      <div v-if="!organizations || organizations.length === 0" class="text-xs text-gray-400">
                        暂无数据
                      </div>
                    </div>
                  </details>
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">组织类型</label>
                <select
                  v-model="form.type"
                  required
                  @change="onTypeChange"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="county">县/区教育局</option>
                  <option value="district">学区中心校</option>
                  <option value="school">学校</option>
                  <option value="kindergarten">幼儿园</option>
                </select>
              </div>

              <!-- 父级组织选择 -->
              <div v-if="availableParents.length > 0">
                <label class="block text-sm font-medium text-gray-700">上级组织</label>
                <select
                  v-model="form.parent_id"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="">请选择上级组织</option>
                  <option
                    v-for="parent in availableParents"
                    :key="parent.id"
                    :value="parent.id"
                  >
                    {{ parent.name }} ({{ getTypeLabel(parent.type) }})
                  </option>
                </select>
                <p class="mt-1 text-xs text-gray-500">
                  {{ getParentHint() }}
                </p>
              </div>
            </div>
            <div class="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                @click="closeModal"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                取消
              </button>
              <button
                type="submit"
                :disabled="submitting"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {{ submitting ? '保存中...' : '保存' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 批量导入模态框 -->
    <div v-if="showImportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">批量导入组织</h3>

          <div class="mb-4">
            <p class="text-sm text-gray-600 mb-2">请选择Excel文件进行批量导入：</p>
            <input
              type="file"
              accept=".xlsx,.xls"
              @change="handleFileSelect"
              class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </div>

          <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p class="text-sm text-yellow-800">
              <strong>导入说明：</strong><br>
              • Excel文件应包含：名称、编码、类型、父级编码列<br>
              • 类型可选：county, district, school, kindergarten<br>
              • 父级编码为空表示顶级组织
            </p>
          </div>

          <div class="flex justify-end space-x-3">
            <button
              @click="closeImportModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
            >
              取消
            </button>
            <button
              @click="handleImport"
              :disabled="!importFile || importing"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 rounded-md"
            >
              {{ importing ? '导入中...' : '开始导入' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import api from '@/utils/api';

const router = useRouter();
const authStore = useAuthStore();

// 响应式数据
const organizations = ref<any[]>([]);
const loading = ref(false);
const submitting = ref(false);
const searchQuery = ref('');
const showCreateModal = ref(false);
const showEditModal = ref(false);
const showImportModal = ref(false);
const editingOrg = ref<any>(null);
const importFile = ref<File | null>(null);
const importing = ref(false);

// 分页数据
const pagination = ref({
  current_page: 1,
  last_page: 1,
  per_page: 15,
  total: 0,
  from: 0,
  to: 0,
});

const form = ref({
  name: '',
  code: '',
  type: 'school',
  parent_id: ''
});

// 计算属性
const filteredOrganizations = computed(() => {
  if (!searchQuery.value) return organizations.value;
  return organizations.value.filter(org =>
    org.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    org.code.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

// 根据当前选择的类型获取可用的父级组织
const availableParents = computed(() => {
  if (!form.value.type) return [];

  const typeHierarchy = {
    'county': [], // 县/区教育局没有上级
    'district': ['county'], // 学区中心校的上级是县/区教育局
    'school': ['district'], // 学校的上级是学区中心校
    'kindergarten': ['district'] // 幼儿园的上级是学区中心校
  };

  const validParentTypes = typeHierarchy[form.value.type] || [];
  if (validParentTypes.length === 0) return [];

  return organizations.value.filter(org =>
    validParentTypes.includes(org.type) &&
    org.id !== editingOrg.value?.id // 排除自己
  );
});

// 方法
const loadOrganizations = async (page = 1) => {
  loading.value = true;
  try {
    const response = await api.get(`/organizations?page=${page}`);

    // 处理分页数据
    if (response.data.success && response.data.data) {
      if (response.data.data.data) {
        // 分页数据结构
        organizations.value = response.data.data.data || [];
        pagination.value = {
          current_page: response.data.data.current_page,
          last_page: response.data.data.last_page,
          per_page: response.data.data.per_page,
          total: response.data.data.total,
          from: response.data.data.from,
          to: response.data.data.to,
        };
      } else if (Array.isArray(response.data.data)) {
        // 直接数组结构
        organizations.value = response.data.data;
      } else {
        organizations.value = [];
      }
    } else {
      organizations.value = [];
    }
  } catch (error) {
    console.error('加载组织列表失败:', error);
    alert('加载组织失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    county: '县/区教育局',
    district: '学区中心校',
    school: '学校',
    kindergarten: '幼儿园'
  };
  return labels[type] || type;
};

const getTypeClass = (type: string) => {
  const classes: Record<string, string> = {
    county: 'bg-purple-100 text-purple-800',
    district: 'bg-blue-100 text-blue-800',
    school: 'bg-green-100 text-green-800',
    kindergarten: 'bg-yellow-100 text-yellow-800'
  };
  return classes[type] || 'bg-gray-100 text-gray-800';
};

const editOrganization = (org: any) => {
  editingOrg.value = org;
  form.value = {
    name: org.name,
    code: org.code,
    type: org.type,
    parent_id: org.parent_id || ''
  };
  showEditModal.value = true;
};

const deleteOrganization = async (org: any) => {
  if (!confirm(`确定要删除组织"${org.name}"吗？`)) return;
  
  try {
    await api.delete(`/organizations/${org.id}`);
    await loadOrganizations();
  } catch (error) {
    console.error('删除组织失败:', error);
    alert('删除失败');
  }
};

// 类型改变时重置父级选择
const onTypeChange = () => {
  form.value.parent_id = '';
};



// 获取父级选择提示
const getParentHint = () => {
  const hints: Record<string, string> = {
    district: '学区中心校应隶属于县/区教育局',
    school: '学校应隶属于学区中心校',
    kindergarten: '幼儿园应隶属于学区中心校'
  };
  return hints[form.value.type] || '';
};

const submitForm = async () => {
  submitting.value = true;
  try {
    if (showCreateModal.value) {
      await api.post('/organizations', form.value);
    } else if (editingOrg.value?.id) {
      await api.put(`/organizations/${editingOrg.value.id}`, form.value);
    } else {
      throw new Error('无效的编辑状态');
    }
    await loadOrganizations();
    closeModal();
  } catch (error: any) {
    console.error('保存失败:', error);
    let errorMessage = '保存失败';

    if (error.response?.status === 422) {
      const errors = error.response.data.errors;
      if (errors?.code) {
        errorMessage = `组织编码 "${form.value.code}" 已被使用，请使用其他编码`;
      } else if (errors?.name) {
        errorMessage = `组织名称 "${form.value.name}" 已被使用，请使用其他名称`;
      } else {
        errorMessage = error.response.data.message || '数据验证失败';
      }
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    alert(errorMessage);
  } finally {
    submitting.value = false;
  }
};

const generateCode = () => {
  const type = form.value.type;
  let prefix = '';

  switch (type) {
    case 'school':
      prefix = 'SCHOOL_';
      break;
    case 'district':
      prefix = 'EDU_DISTRICT_';
      break;
    case 'kindergarten':
      prefix = 'KINDERGARTEN_';
      break;
    case 'county':
      prefix = 'EDU_COUNTY_';
      break;
    default:
      prefix = 'ORG_';
  }

  // 找到该类型的最大编号
  const existingCodes = (organizations.value || [])
    .filter(org => org?.code?.startsWith(prefix))
    .map(org => {
      const match = org.code.match(new RegExp(`${prefix}(\\d+)`));
      return match ? parseInt(match[1]) : 0;
    });

  const maxNumber = existingCodes.length > 0 ? Math.max(...existingCodes) : 0;
  const nextNumber = String(maxNumber + 1).padStart(3, '0');

  form.value.code = prefix + nextNumber;
};

const closeModal = () => {
  showCreateModal.value = false;
  showEditModal.value = false;
  editingOrg.value = null;
  form.value = {
    name: '',
    code: '',
    type: 'school',
    parent_id: ''
  };
};

// 批量导入相关方法
const downloadTemplate = () => {
  // 创建Excel模板数据
  const templateData = [
    ['名称', '编码', '类型', '父级编码'],
    ['示例学区', '130182999', 'district', '130182000'],
    ['示例学校', '130182998', 'school', '130182999'],
  ];

  // 创建CSV内容
  const csvContent = templateData.map(row => row.join(',')).join('\n');
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', '组织导入模板.csv');
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    importFile.value = target.files[0];
  }
};

const closeImportModal = () => {
  showImportModal.value = false;
  importFile.value = null;
};

const handleImport = async () => {
  if (!importFile.value) return;

  importing.value = true;
  try {
    const formData = new FormData();
    formData.append('file', importFile.value);

    const response = await api.post('/organizations/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (response.data.success) {
      alert(`导入成功！共导入 ${response.data.imported_count} 个组织`);
      await loadOrganizations();
      closeImportModal();
    } else {
      alert('导入失败：' + (response.data.message || '未知错误'));
    }
  } catch (error: any) {
    console.error('导入失败:', error);
    alert('导入失败：' + (error.response?.data?.message || error.message || '网络错误'));
  } finally {
    importing.value = false;
  }
};

const handleLogout = async () => {
  await authStore.logout();
  router.push('/login');
};

// 监听组织类型变化，自动生成代码
watch(() => form.value.type, () => {
  if (showCreateModal.value && !form.value.code) {
    generateCode();
  }
});

onMounted(() => {
  loadOrganizations();
});
</script>
