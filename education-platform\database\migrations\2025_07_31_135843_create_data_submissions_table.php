<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('data_submissions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('task_id')->comment('任务ID');
            $table->unsignedBigInteger('organization_id')->comment('提交组织ID');
            $table->unsignedBigInteger('submitter_id')->comment('提交者ID');
            $table->json('data')->comment('提交的数据');
            $table->enum('status', ['draft', 'submitted', 'reviewing', 'approved', 'rejected'])->default('draft')->comment('状态');
            $table->integer('version')->default(1)->comment('版本号');
            $table->timestamp('submit_time')->nullable()->comment('提交时间');
            $table->timestamp('review_time')->nullable()->comment('审核时间');
            $table->unsignedBigInteger('reviewer_id')->nullable()->comment('审核者ID');
            $table->text('review_comments')->nullable()->comment('审核意见');
            $table->string('data_hash', 64)->nullable()->comment('数据完整性校验');
            $table->timestamps();

            // 索引
            $table->index(['task_id', 'organization_id']);
            $table->index('status');
            $table->index('submit_time');
            $table->index('submitter_id');

            // 外键约束
            $table->foreign('task_id')->references('id')->on('statistics_tasks')->onDelete('cascade');
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('cascade');
            $table->foreign('submitter_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('reviewer_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('data_submissions');
    }
};
