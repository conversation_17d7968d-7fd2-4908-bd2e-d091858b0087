<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FormTemplate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class FormTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = FormTemplate::with(['creator', 'organization']);

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        if ($request->has('category')) {
            $query->where('category', $request->get('category'));
        }

        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        $templates = $query->orderBy('created_at', 'desc')
                          ->paginate($request->get('per_page', 15));

        // Transform data to match frontend expectations
        $templates->getCollection()->transform(function ($template) {
            return [
                'id' => $template->id,
                'name' => $template->name,
                'description' => $template->description,
                'category' => $template->category,
                'is_active' => $template->status === 'published',
                'status' => $template->status,
                'field_count' => isset($template->config['fields']) ? count($template->config['fields']) : 0,
                'fields' => $template->config['fields'] ?? [],
                'created_at' => $template->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $template->updated_at->format('Y-m-d H:i:s'),
                'creator' => $template->creator ? [
                    'id' => $template->creator->id,
                    'name' => $template->creator->name,
                ] : null,
                'organization' => $template->organization ? [
                    'id' => $template->organization->id,
                    'name' => $template->organization->name,
                ] : null,
                'usage_count' => $template->usage_count,
                'version' => $template->version,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $templates
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|string|max:100',
            'description' => 'nullable|string',
            'config' => 'required|array',
            'validation_rules' => 'nullable|array',
            'conditional_logic' => 'nullable|array',
        ]);

        $template = FormTemplate::create([
            'name' => $request->name,
            'category' => $request->category,
            'description' => $request->description,
            'config' => $request->config,
            'validation_rules' => $request->validation_rules,
            'conditional_logic' => $request->conditional_logic,
            'version' => '1.0',
            'status' => 'draft',
            'creator_id' => auth()->id(),
            'organization_id' => auth()->user()->organization_id,
            'usage_count' => 0
        ]);

        $template->load(['creator', 'organization']);

        return response()->json([
            'success' => true,
            'message' => '表单模板创建成功',
            'data' => [
                'id' => $template->id,
                'name' => $template->name,
                'description' => $template->description,
                'category' => $template->category,
                'is_active' => $template->status === 'published',
                'status' => $template->status,
                'field_count' => isset($template->config['fields']) ? count($template->config['fields']) : 0,
                'fields' => $template->config['fields'] ?? [],
                'created_at' => $template->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $template->updated_at->format('Y-m-d H:i:s'),
                'creator' => $template->creator ? [
                    'id' => $template->creator->id,
                    'name' => $template->creator->name,
                ] : null,
                'organization' => $template->organization ? [
                    'id' => $template->organization->id,
                    'name' => $template->organization->name,
                ] : null,
                'usage_count' => $template->usage_count,
                'version' => $template->version,
            ]
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(FormTemplate $formTemplate): JsonResponse
    {
        $formTemplate->load(['creator', 'organization', 'statisticsTasks']);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $formTemplate->id,
                'name' => $formTemplate->name,
                'description' => $formTemplate->description,
                'category' => $formTemplate->category,
                'is_active' => $formTemplate->status === 'published',
                'status' => $formTemplate->status,
                'field_count' => isset($formTemplate->config['fields']) ? count($formTemplate->config['fields']) : 0,
                'fields' => $formTemplate->config['fields'] ?? [],
                'config' => $formTemplate->config,
                'validation_rules' => $formTemplate->validation_rules,
                'conditional_logic' => $formTemplate->conditional_logic,
                'version' => $formTemplate->version,
                'created_at' => $formTemplate->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $formTemplate->updated_at->format('Y-m-d H:i:s'),
                'creator' => $formTemplate->creator ? [
                    'id' => $formTemplate->creator->id,
                    'name' => $formTemplate->creator->name,
                ] : null,
                'organization' => $formTemplate->organization ? [
                    'id' => $formTemplate->organization->id,
                    'name' => $formTemplate->organization->name,
                ] : null,
                'usage_count' => $formTemplate->usage_count,
                'statistics_tasks' => $formTemplate->statisticsTasks->map(function ($task) {
                    return [
                        'id' => $task->id,
                        'title' => $task->title,
                        'status' => $task->status,
                        'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    ];
                }),
            ]
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, FormTemplate $formTemplate): JsonResponse
    {
        if (!$formTemplate->canEdit()) {
            return response()->json([
                'success' => false,
                'message' => '该模板正在使用中，无法编辑'
            ], 422);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|string|max:100',
            'description' => 'nullable|string',
            'config' => 'required|array',
            'validation_rules' => 'nullable|array',
            'conditional_logic' => 'nullable|array',
        ]);

        $formTemplate->update([
            'name' => $request->name,
            'category' => $request->category,
            'description' => $request->description,
            'config' => $request->config,
            'validation_rules' => $request->validation_rules,
            'conditional_logic' => $request->conditional_logic,
        ]);

        $formTemplate->load(['creator', 'organization']);

        return response()->json([
            'success' => true,
            'message' => '表单模板更新成功',
            'data' => [
                'id' => $formTemplate->id,
                'name' => $formTemplate->name,
                'description' => $formTemplate->description,
                'category' => $formTemplate->category,
                'is_active' => $formTemplate->status === 'published',
                'status' => $formTemplate->status,
                'field_count' => isset($formTemplate->config['fields']) ? count($formTemplate->config['fields']) : 0,
                'fields' => $formTemplate->config['fields'] ?? [],
                'created_at' => $formTemplate->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $formTemplate->updated_at->format('Y-m-d H:i:s'),
                'creator' => $formTemplate->creator ? [
                    'id' => $formTemplate->creator->id,
                    'name' => $formTemplate->creator->name,
                ] : null,
                'organization' => $formTemplate->organization ? [
                    'id' => $formTemplate->organization->id,
                    'name' => $formTemplate->organization->name,
                ] : null,
                'usage_count' => $formTemplate->usage_count,
                'version' => $formTemplate->version,
            ]
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FormTemplate $formTemplate): JsonResponse
    {
        if (!$formTemplate->canEdit()) {
            return response()->json([
                'success' => false,
                'message' => '该模板正在使用中，无法删除'
            ], 422);
        }

        $formTemplate->delete();

        return response()->json([
            'success' => true,
            'message' => '表单模板删除成功'
        ]);
    }

    /**
     * 发布模板
     */
    public function publish(FormTemplate $formTemplate): JsonResponse
    {
        if ($formTemplate->publish()) {
            return response()->json([
                'success' => true,
                'message' => '表单模板发布成功'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => '表单模板发布失败'
        ], 422);
    }

    /**
     * 归档模板
     */
    public function archive(FormTemplate $formTemplate): JsonResponse
    {
        if ($formTemplate->archive()) {
            return response()->json([
                'success' => true,
                'message' => '表单模板归档成功'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => '表单模板归档失败'
        ], 422);
    }

    /**
     * 复制模板
     */
    public function duplicate(FormTemplate $formTemplate): JsonResponse
    {
        $newTemplate = FormTemplate::create([
            'name' => $formTemplate->name . ' (副本)',
            'category' => $formTemplate->category,
            'description' => $formTemplate->description,
            'config' => $formTemplate->config,
            'validation_rules' => $formTemplate->validation_rules,
            'conditional_logic' => $formTemplate->conditional_logic,
            'version' => '1.0',
            'status' => 'draft',
            'creator_id' => auth()->id(),
            'organization_id' => auth()->user()->organization_id,
            'usage_count' => 0
        ]);

        $newTemplate->load(['creator', 'organization']);

        return response()->json([
            'success' => true,
            'message' => '表单模板复制成功',
            'data' => [
                'id' => $newTemplate->id,
                'name' => $newTemplate->name,
                'description' => $newTemplate->description,
                'category' => $newTemplate->category,
                'is_active' => $newTemplate->status === 'published',
                'status' => $newTemplate->status,
                'field_count' => isset($newTemplate->config['fields']) ? count($newTemplate->config['fields']) : 0,
                'fields' => $newTemplate->config['fields'] ?? [],
                'created_at' => $newTemplate->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $newTemplate->updated_at->format('Y-m-d H:i:s'),
                'creator' => $newTemplate->creator ? [
                    'id' => $newTemplate->creator->id,
                    'name' => $newTemplate->creator->name,
                ] : null,
                'organization' => $newTemplate->organization ? [
                    'id' => $newTemplate->organization->id,
                    'name' => $newTemplate->organization->name,
                ] : null,
                'usage_count' => $newTemplate->usage_count,
                'version' => $newTemplate->version,
            ]
        ], 201);
    }

    /**
     * 预览模板
     */
    public function preview(FormTemplate $formTemplate): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'name' => $formTemplate->name,
                'description' => $formTemplate->description,
                'config' => $formTemplate->config,
                'validation_rules' => $formTemplate->validation_rules,
                'conditional_logic' => $formTemplate->conditional_logic,
            ]
        ]);
    }
}
