import{d as T,u as q,r as m,g as $,c as i,a as t,i as _,j as w,k as N,t as p,h as O,w as j,e as n,l as r,v as c,q as k,F as h,p as S,f as B,s as g,o as d,z as F}from"./app-D0Qwllno.js";const I={class:"min-h-screen bg-gray-50"},L={class:"bg-white shadow"},A={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},E={class:"flex justify-between h-16"},P={class:"flex items-center space-x-4"},R={class:"flex items-center space-x-4"},W={class:"text-sm text-gray-700"},G={class:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8"},H={class:"px-4 py-6 sm:px-0"},J={class:"bg-white shadow rounded-lg p-6"},K={class:"mb-4"},Q={class:"mb-4"},X=["value"],Y={class:"mb-4"},Z={class:"space-y-2 max-h-48 overflow-y-auto border border-gray-300 rounded-md p-3"},tt=["id","value"],et=["for"],st={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},ot={class:"mb-4"},at={class:"mb-4"},lt={class:"flex justify-end space-x-4"},nt=["disabled"],dt=T({__name:"Create",setup(rt){const x=B(),v=q(),u=m(!1),f=m([]),y=m([]),a=m({title:"",form_template_id:"",target_organizations:[],start_time:"",end_time:"",priority:"normal",instructions:""}),V=async()=>{await v.logout(),x.push("/login")},z=async()=>{try{const o=await g.get("/form-templates");f.value=o.data.data.data||[]}catch(o){console.error("加载表单模板失败:",o)}},U=async()=>{try{const o=await g.get("/organizations");y.value=o.data.data.data||[]}catch(o){console.error("加载组织列表失败:",o)}},C=o=>{const e="  ".repeat(o.level-1),l={county:"县/区教育局",district:"学区中心校",school:"学校",kindergarten:"幼儿园"};return`${e}${o.name} (${l[o.type]||o.type})`},D=async()=>{var o,e;if(a.value.target_organizations.length===0){alert("请至少选择一个目标组织");return}u.value=!0;try{await g.post("/statistics-tasks",a.value),x.push("/statistics-tasks")}catch(l){console.error("创建任务失败:",l),alert("创建失败: "+(((e=(o=l.response)==null?void 0:o.data)==null?void 0:e.message)||l.message))}finally{u.value=!1}};return $(()=>{z(),U();const o=new Date,e=new Date(o.getTime()+1440*60*1e3),l=new Date(o.getTime()+10080*60*1e3);a.value.start_time=e.toISOString().slice(0,16),a.value.end_time=l.toISOString().slice(0,16)}),(o,e)=>{var b;const l=N("router-link");return d(),i("div",I,[t("nav",L,[t("div",A,[t("div",E,[t("div",P,[_(l,{to:"/statistics-tasks",class:"text-indigo-600 hover:text-indigo-500"},{default:w(()=>e[7]||(e[7]=[r(" ← 返回列表 ",-1)])),_:1,__:[7]}),e[8]||(e[8]=t("h1",{class:"text-xl font-semibold text-gray-900"}," 创建统计任务 ",-1))]),t("div",R,[t("span",W,p((b=O(v).user)==null?void 0:b.name),1),t("button",{onClick:V,class:"text-sm text-gray-500 hover:text-gray-700"}," 退出登录 ")])])])]),t("div",G,[t("div",H,[t("form",{onSubmit:j(D,["prevent"]),class:"space-y-6"},[t("div",J,[e[18]||(e[18]=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"基本信息",-1)),t("div",K,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},[r(" 任务标题 "),t("span",{class:"text-red-500"},"*")],-1)),n(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>a.value.title=s),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500",placeholder:"请输入任务标题"},null,512),[[c,a.value.title]])]),t("div",Q,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},[r(" 表单模板 "),t("span",{class:"text-red-500"},"*")],-1)),n(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>a.value.form_template_id=s),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"},[e[10]||(e[10]=t("option",{value:""},"请选择表单模板",-1)),(d(!0),i(h,null,S(f.value,s=>(d(),i("option",{key:s.id,value:s.id},p(s.name),9,X))),128))],512),[[k,a.value.form_template_id]])]),t("div",Y,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},[r(" 目标组织 "),t("span",{class:"text-red-500"},"*")],-1)),t("div",Z,[(d(!0),i(h,null,S(y.value,s=>(d(),i("div",{key:s.id,class:"flex items-center"},[n(t("input",{id:`org-${s.id}`,"onUpdate:modelValue":e[2]||(e[2]=M=>a.value.target_organizations=M),value:s.id,type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,8,tt),[[F,a.value.target_organizations]]),t("label",{for:`org-${s.id}`,class:"ml-2 text-sm text-gray-700 cursor-pointer"},p(C(s)),9,et)]))),128))])]),t("div",st,[t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},[r(" 开始时间 "),t("span",{class:"text-red-500"},"*")],-1)),n(t("input",{"onUpdate:modelValue":e[3]||(e[3]=s=>a.value.start_time=s),type:"datetime-local",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[c,a.value.start_time]])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},[r(" 结束时间 "),t("span",{class:"text-red-500"},"*")],-1)),n(t("input",{"onUpdate:modelValue":e[4]||(e[4]=s=>a.value.end_time=s),type:"datetime-local",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[c,a.value.end_time]])])]),t("div",ot,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 优先级 ",-1)),n(t("select",{"onUpdate:modelValue":e[5]||(e[5]=s=>a.value.priority=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"},e[15]||(e[15]=[t("option",{value:"low"},"低",-1),t("option",{value:"normal"},"普通",-1),t("option",{value:"high"},"高",-1),t("option",{value:"urgent"},"紧急",-1)]),512),[[k,a.value.priority]])]),t("div",at,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 任务说明 ",-1)),n(t("textarea",{"onUpdate:modelValue":e[6]||(e[6]=s=>a.value.instructions=s),rows:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500",placeholder:"请输入任务说明和填报要求"},null,512),[[c,a.value.instructions]])])]),t("div",lt,[_(l,{to:"/statistics-tasks",class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"},{default:w(()=>e[19]||(e[19]=[r(" 取消 ",-1)])),_:1,__:[19]}),t("button",{type:"submit",disabled:u.value,class:"px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50"},p(u.value?"创建中...":"创建任务"),9,nt)])],32)])])])}}});export{dt as default};
