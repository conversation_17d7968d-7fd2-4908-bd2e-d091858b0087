# 教育数据统计管理平台 - 详细设计方案

## 一、 总体架构与技术选型

本平台旨在构建一个支持三级管理体系（区县教育局 → 学区中心校 → 中小学/幼儿园）的、功能完善的教育数据统计管理平台。

### 1.1 技术栈

- **前端**: `Vue 3 + TypeScript + Element Plus`
  - **优势**: 组件化开发、类型安全、成熟美观的UI组件库。
  - **建议扩展**:
    - **数据可视化**: `Apache ECharts` 或 `AntV G2`
    - **表单设计器**: 可选用成熟的开源库如 `form-create`，或基于 `Vue` 动态组件自研。
    - **状态管理**: `Pinia`
    - **拖拽布局**: `GridStack.js` (用于自定义仪表板)

- **后端**: `Laravel 12 + MySQL + JWT`
  - **优势**: 高效的开发框架、可靠的数据库、现代化的无状态认证。
  - **建议扩展**:
    - **异步任务**: `Laravel Queues` + `Redis` (处理报表生成、批量通知等耗时任务)。
    - **权限控制**: `Laravel Gates & Policies` (实现精细化的RBAC)。
    - **API文档**: `Scribe` 或 `Swagger` (自动生成API文档)。

### 1.2 组织架构实现

- **数据库设计**: 在 `organizations` (组织机构) 表中，使用 `parent_id` 字段实现无限级树状结构，完美支持您的管理体系。
- **数据权限**: 后端通过用户所属 `organization_id` 和递归查询，高效获取其所有下级单位，实现“上级可查看下级所有数据”的权限模型。

---

## 二、 详细功能模块设计

### 模块1：统计表定制系统 (表单引擎)

**目标**: 灵活、强大，让业务股室人员可自行设计统计表。

#### 1.1 基础功能

- **可视化设计器**: 拖拽式界面，从组件库拖拽组件（文本、数字、单/多选、日期、附件等）到画布。
- **模板管理**: 各股室可创建、编辑、另存为、删除统计表模板。
- **基础设置**: 可配置模板名称、说明、填报要求、默认截止时间等。
- **字段属性**: 可为每个组件设置标题、占位符、是否必填等。

#### 1.2 高级功能

- **条件逻辑 (Conditional Logic)**
  - **描述**: 根据字段填报内容，动态显示/隐藏其他字段。
  - **实现**: 前端使用 `watch` 监听数据变化，动态渲染表单；后端进行逻辑一致性校验。
- **高级数据校验 (Data Validation)**
  - **描述**: 除必填外，支持身份证号、手机号、数值范围、自定义正则表达式等校验规则。
  - **实现**: 前端使用 `Element Plus` 自带或自定义校验器；后端使用 `Laravel Form Request Validation` 进行双重强校验。
- **关联计算 (Relational Calculation)**
  - **描述**: 支持在表单内设置计算公式，如 `总人数 = 男生人数 + 女生人数`。
  - **实现**: 前端JS实时计算；后端保存时根据公式二次运算，确保数据准确。
- **数据源关联 (Data Source Association)**
  - **描述**: 字段选项可从其他系统或本系统已有数据中动态获取（如从用户表选择填报人）。
  - **实现**: 后端提供专门的数据源API接口供表单设计器调用。

### 模块2：任务下发与管理

**目标**: 流程清晰，指派灵活，管理高效。

- **任务创建**: 基于模板创建任务，设定独立的起止时间、填报说明。
- **对象选择**: 通过组织架构树，灵活勾选下发对象（可跨学区、可选择整个学区、可到具体学校）。
- **分层下发**: 支持区县 -> 学区 -> 学校的二级下发模式。
- **周期性任务**: 支持设置“每月”、“每学期”等周期任务，到期自动创建。
- **版本控制**: 支持对已下发的表单模板进行版本更新，并通知相关单位。

### 模块3：填报进度监控

**目标**: 实时、直观、可催办。

- **进度概览**: 以列表、进度条、百分比展示任务整体完成率。
- **可视化监控大屏**: 使用地图或色块图展示各单位填报进度（绿: 已提交, 黄: 暂存, 红: 逾期）。
- **一键催办/提醒**:
  - **功能**: 选中未完成单位，批量发送催办提醒。
  - **通道**: 平台内消息、短信、或集成钉钉/企业微信。
- **催办记录**: 记录催办历史，避免重复操作和管理混乱。

### 模块4：数据汇总与分析 (数据可视化)

**目标**: 自动化、多维度、智能化，为决策提供支持。

- **自动汇总**: 按题目选项自动进行分类、计数、求和、求平均值等。
- **多维分析**:
  - **交叉分析**: 选择任意2个以上维度进行数据交叉透视。
  - **趋势分析**: 对周期性报表数据，自动生成关键指标随时间变化的趋势图。
  - **对比分析**: 选择多个单位进行同指标的横向对比（雷达图、分组柱状图）。
  - **数据钻取**: 在图表上支持从宏观到微观的逐级下钻查看（区县 -> 学区 -> 学校）。
- **自定义仪表板 (Dashboard)**
  - **描述**: 允许用户（特别是领导）拖拽关心的图表组件，自由布局个人工作台首页。
  - **实现**: 前端使用 `ECharts` + `GridStack.js`；后端为每个图表组件封装独立的数据API。
- **移动端适配**: 核心仪表板和报表应支持在移动设备上良好展示。

### 模块5：报表导出功能

- **格式支持**: `Excel`, `PDF`。
- **维度导出**: 支持按区县、学区、学校等不同维度导出数据包。
- **自定义模板**: 支持导出带有教育局官方页眉页脚的格式化报表。
- **原始数据导出**: 支持导出 `CSV` 格式的原始数据，便于离线进行深度分析。

---

## 三、 核心保障机制

### 3.1 数据质量控制机制

- **多级审核流程**:
  - **流程**: 学校填报 -> (学区审核) -> 教育局股室审核 -> 归档。
  - **操作**: 支持“通过”和“驳回”（需填写驳回理由）。
- **数据锁定**: 审核通过的数据即被锁定，防止篡改。如需修改，需走解锁申请流程。
- **异常数据检测**:
  - **规则预警**: 设置规则（如“生师比”异常范围、数据相比上期波动超50%），系统自动高亮标记。
- **数据溯源**: 所有数据的新增、修改、审核操作均记录详细日志，确保可追溯。

### 3.2 系统管理功能

- **用户与权限管理 (RBAC)**
  - **角色管理**: 创建角色（局长、股室科员、学区管理员、学校填报员）并分配菜单/按钮权限。
  - **数据权限**: 将用户绑定到组织机构节点，自动继承数据查看权限。
- **操作日志**: 详细记录所有用户的关键操作（登录、数据操作、任务下发等）。
- **系统配置**: 系统名称、Logo、第三方服务（短信、邮件）配置等。

---

## 四、 系统对接与扩展

### 4.1 与现有教育系统的数据对接

- **首选方案: API对接**
  - **描述**: 平台对外提供标准RESTful API，实现与其他业务系统（学籍、人事、实验教学）的数据互通。
  - **场景**: 从学籍系统同步学生数作为填报校验基准；实验教学平台调用本平台接口获取生均数据。
- **备选方案: 数据中间库**
  - **描述**: 对于遗留系统，可通过ETL工具定期将数据抽取到中间库，本平台再从中间库同步。
- **用户体验: 单点登录 (SSO)**
  - **描述**: 优先接入教育局统一身份认证平台，实现一次登录、多系统漫游。

### 4.2 创新功能建议

- **智能助手**: 内置问答机器人，解答填报疑问。
- **数据订阅与推送**: 用户可订阅关心的指标，数据更新后自动生成简报推送到指定渠道。
- **自然语言生成 (NLG)**: 对图表进行简单的文字性解读，自动生成分析摘要。

---

## 五、 实施路线图 (Roadmap)

建议采用敏捷开发，分期上线。

- **一期: 核心流程贯通 (MVP)**
  - **内容**: 组织架构、用户权限、表单定制(基础)、任务下发与填报、基本数据汇总与导出。
  - **目标**: 解决数据在线收集的核心痛点。

- **二期: 数据质量与分析深化**
  - **内容**: 多级审核流程、异常数据检测、自定义仪表板、多维分析、数据钻取。
  - **目标**: 从“收数据”转向“用好数据”，提升数据可信度和价值。

- **三期: 平台融合与智能化**
  - **内容**: 对外API、单点登录、数据对接、智能助手、数据订阅等。
  - **目标**: 打破数据孤岛，全面提升平台智能化水平和管理效率。