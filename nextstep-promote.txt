Please create a comprehensive project documentation file that serves as a handoff document for continuing development in a new conversation. This document should include:

1. **Project Overview**: Brief description of the education data statistics management platform and its purpose

2. **Technical Stack Summary**: List all technologies, frameworks, and versions currently used (Laravel, Vue 3, TypeScript, etc.)

3. **Completed Features Documentation**: 
   - Detailed list of all implemented frontend and backend features
   - File structure and key components created
   - API endpoints that are functional
   - Database schema and models implemented

4. **Current Development Status**:
   - What is currently working and tested
   - What has been implemented but may need testing
   - Any known issues or limitations

5. **Next Development Phase Requirements**:
   - Specific features that need to be implemented next
   - Priority order for upcoming tasks
   - Any dependencies or prerequisites for next steps

6. **Setup Instructions**: 
   - How to get the project running locally
   - Database configuration details
   - Environment setup requirements

7. **Context Prompt for New Conversation**: 
   Write a detailed prompt that can be used to start a new conversation, including:
   - Complete project context and current state
   - Technical specifications and architecture decisions
   - Immediate next steps to continue development
   - Any important implementation details or conventions established

The goal is to ensure seamless continuity when development resumes in a fresh conversation, maintaining consistency in coding standards, architecture decisions, and project direction.