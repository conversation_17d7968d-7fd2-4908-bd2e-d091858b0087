<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('form_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('模板名称');
            $table->string('category', 100)->comment('模板分类');
            $table->text('description')->nullable()->comment('模板描述');
            $table->json('config')->comment('表单配置');
            $table->json('validation_rules')->nullable()->comment('验证规则');
            $table->json('conditional_logic')->nullable()->comment('条件逻辑');
            $table->string('version', 20)->default('1.0')->comment('版本号');
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft')->comment('状态');
            $table->unsignedBigInteger('creator_id')->comment('创建者ID');
            $table->unsignedBigInteger('organization_id')->comment('所属组织ID');
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->timestamps();

            // 索引
            $table->index('category');
            $table->index('status');
            $table->index('creator_id');
            $table->index('organization_id');

            // 外键约束
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('form_templates');
    }
};
