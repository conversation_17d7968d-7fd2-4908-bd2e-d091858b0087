<?php

namespace Database\Seeders;

use App\Models\Organization;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class OrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建区县教育局
        $county = Organization::create([
            'name' => '示例区教育局',
            'code' => 'EDU_COUNTY_001',
            'type' => 'county',
            'parent_id' => null,
            'level' => 1,
            'sort_order' => 1,
            'contact_info' => [
                'address' => '示例区政府大楼',
                'phone' => '0123-12345678',
                'email' => '<EMAIL>'
            ],
            'status' => 1
        ]);

        // 创建学区中心校
        $district1 = Organization::create([
            'name' => '第一学区中心校',
            'code' => 'EDU_DISTRICT_001',
            'type' => 'district',
            'parent_id' => $county->id,
            'level' => 2,
            'sort_order' => 1,
            'contact_info' => [
                'address' => '第一学区办公楼',
                'phone' => '0123-11111111',
                'email' => '<EMAIL>'
            ],
            'status' => 1
        ]);

        $district2 = Organization::create([
            'name' => '第二学区中心校',
            'code' => 'EDU_DISTRICT_002',
            'type' => 'district',
            'parent_id' => $county->id,
            'level' => 2,
            'sort_order' => 2,
            'contact_info' => [
                'address' => '第二学区办公楼',
                'phone' => '0123-22222222',
                'email' => '<EMAIL>'
            ],
            'status' => 1
        ]);

        // 创建学校
        $schools = [
            ['name' => '示例小学', 'code' => 'SCHOOL_001', 'district' => $district1],
            ['name' => '示例中学', 'code' => 'SCHOOL_002', 'district' => $district1],
            ['name' => '示例高中', 'code' => 'SCHOOL_003', 'district' => $district2],
            ['name' => '示例幼儿园', 'code' => 'KINDERGARTEN_001', 'district' => $district2, 'type' => 'kindergarten'],
        ];

        foreach ($schools as $index => $schoolData) {
            Organization::create([
                'name' => $schoolData['name'],
                'code' => $schoolData['code'],
                'type' => $schoolData['type'] ?? 'school',
                'parent_id' => $schoolData['district']->id,
                'level' => 3,
                'sort_order' => $index + 1,
                'contact_info' => [
                    'address' => $schoolData['name'] . '校址',
                    'phone' => '0123-' . str_pad($index + 1, 8, '0', STR_PAD_LEFT),
                    'email' => strtolower(str_replace(['示例', '学', '中', '小', '高', '幼儿园'], ['', '', '', '', '', 'kg'], $schoolData['name'])) . '@school.edu.cn'
                ],
                'status' => 1
            ]);
        }
    }
}
