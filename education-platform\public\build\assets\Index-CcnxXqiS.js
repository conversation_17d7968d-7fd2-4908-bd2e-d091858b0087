import{d as P,u as U,r as l,m as q,g as F,c as n,a as t,i as C,j as M,k as A,t as a,h as E,b as m,e as y,v as I,q as $,y as Q,F as R,p as G,f as J,s as S,o as i,l as K,x as T,D as O}from"./app-D0Qwllno.js";import{d as W}from"./dayjs.min-Cbbdfn5l.js";const X={class:"min-h-screen bg-gray-50"},Z={class:"bg-white shadow"},tt={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},et={class:"flex justify-between h-16"},st={class:"flex items-center space-x-4"},ot={class:"flex items-center space-x-4"},at={class:"text-sm text-gray-700"},rt={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},lt={class:"px-4 py-6 sm:px-0"},nt={class:"mb-6 flex justify-between items-center"},it={class:"flex space-x-4"},dt={class:"relative"},ct={class:"bg-white shadow rounded-lg overflow-hidden"},pt={class:"min-w-full divide-y divide-gray-200"},ut={class:"bg-white divide-y divide-gray-200"},xt={class:"px-6 py-4 whitespace-nowrap"},gt={class:"text-sm font-medium text-gray-900"},mt={class:"text-sm text-gray-500"},yt={class:"px-6 py-4 whitespace-nowrap"},vt={class:"text-sm text-gray-900"},_t={class:"px-6 py-4 whitespace-nowrap"},ht={class:"text-sm text-gray-900"},ft={class:"px-6 py-4 whitespace-nowrap"},bt={class:"px-6 py-4 whitespace-nowrap"},wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},kt={class:"px-6 py-4 whitespace-nowrap"},Ct={class:"flex items-center"},Mt={class:"w-16 bg-gray-200 rounded-full h-2 mr-2"},$t={class:"text-sm text-gray-500"},St={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Tt={class:"flex space-x-2"},Vt=["onClick"],jt=["onClick"],Bt=["onClick"],Dt={key:0,class:"text-center py-12"},Lt={key:0,class:"mt-6 flex items-center justify-between"},Nt={class:"text-sm text-gray-700"},zt={class:"flex space-x-2"},Ht=["disabled"],Yt=["disabled"],Ft=P({__name:"Index",setup(Pt){const g=J(),v=U(),_=l([]),c=l(""),p=l(""),u=l(""),h=l(!1);l(!1);const r=l({current_page:1,last_page:1,per_page:15,total:0,from:0,to:0}),f=q(()=>{let s=_.value;if(c.value){const e=c.value.toLowerCase();s=s.filter(d=>d.title.toLowerCase().includes(e)||d.description.toLowerCase().includes(e))}return p.value&&(s=s.filter(e=>e.status===p.value)),u.value&&(s=s.filter(e=>e.priority===u.value)),s}),V=async()=>{await v.logout(),g.push("/login")},j=s=>W(s).format("YYYY-MM-DD HH:mm"),B=s=>({draft:"草稿",published:"已发布",in_progress:"进行中",completed:"已完成",cancelled:"已取消"})[s]||s,D=s=>({draft:"bg-gray-100 text-gray-800",published:"bg-blue-100 text-blue-800",in_progress:"bg-yellow-100 text-yellow-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",L=s=>({high:"高",medium:"中",low:"低"})[s]||s,N=s=>({high:"bg-red-100 text-red-800",medium:"bg-yellow-100 text-yellow-800",low:"bg-green-100 text-green-800"})[s]||"bg-gray-100 text-gray-800",x=async(s=1)=>{try{h.value=!0;const e=await S.get(`/statistics-tasks?page=${s}`);_.value=e.data.data||[],e.data.meta&&(r.value={current_page:e.data.meta.current_page,last_page:e.data.meta.last_page,per_page:e.data.meta.per_page,total:e.data.meta.total,from:(e.data.meta.current_page-1)*e.data.meta.per_page+1,to:Math.min(e.data.meta.current_page*e.data.meta.per_page,e.data.meta.total)})}catch(e){console.error("加载统计任务失败:",e)}finally{h.value=!1}},z=s=>{g.push(`/statistics-tasks/${s.id}`)},H=s=>{g.push(`/statistics-tasks/${s.id}/edit`)},Y=async s=>{if(confirm(`确定要发布任务 "${s.title}" 吗？`))try{await S.patch(`/statistics-tasks/${s.id}/publish`),await x()}catch(e){console.error("发布任务失败:",e)}};return F(()=>{x()}),(s,e)=>{var b;const d=A("router-link");return i(),n("div",X,[t("nav",Z,[t("div",tt,[t("div",et,[t("div",st,[C(d,{to:"/dashboard",class:"text-indigo-600 hover:text-indigo-500"},{default:M(()=>e[5]||(e[5]=[K(" ← 返回首页 ",-1)])),_:1,__:[5]}),e[6]||(e[6]=t("h1",{class:"text-xl font-semibold text-gray-900"}," 统计任务管理 ",-1))]),t("div",ot,[t("span",at,a((b=E(v).user)==null?void 0:b.name),1),t("button",{onClick:V,class:"text-sm text-gray-500 hover:text-gray-700"}," 退出登录 ")])])])]),t("div",rt,[t("div",lt,[t("div",nt,[t("div",it,[t("div",dt,[y(t("input",{"onUpdate:modelValue":e[0]||(e[0]=o=>c.value=o),type:"text",placeholder:"搜索统计任务...",class:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[I,c.value]]),e[7]||(e[7]=t("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[t("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1))]),y(t("select",{"onUpdate:modelValue":e[1]||(e[1]=o=>p.value=o),class:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},e[8]||(e[8]=[Q('<option value="">所有状态</option><option value="draft">草稿</option><option value="published">已发布</option><option value="in_progress">进行中</option><option value="completed">已完成</option><option value="cancelled">已取消</option>',6)]),512),[[$,p.value]]),y(t("select",{"onUpdate:modelValue":e[2]||(e[2]=o=>u.value=o),class:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},e[9]||(e[9]=[t("option",{value:""},"所有优先级",-1),t("option",{value:"high"},"高",-1),t("option",{value:"medium"},"中",-1),t("option",{value:"low"},"低",-1)]),512),[[$,u.value]])]),C(d,{to:"/statistics-tasks/create",class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"},{default:M(()=>e[10]||(e[10]=[t("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),t("span",null,"创建任务",-1)])),_:1,__:[10]})]),t("div",ct,[t("table",pt,[e[11]||(e[11]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"任务信息"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"表单模板"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"目标机构"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"状态"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"优先级"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"截止时间"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"进度"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"操作")])],-1)),t("tbody",ut,[(i(!0),n(R,null,G(f.value,o=>{var w;return i(),n("tr",{key:o.id,class:"hover:bg-gray-50"},[t("td",xt,[t("div",null,[t("div",gt,a(o.title),1),t("div",mt,a(o.description),1)])]),t("td",yt,[t("div",vt,a(((w=o.form_template)==null?void 0:w.name)||"-"),1)]),t("td",_t,[t("div",ht,a(o.target_organizations_count)+"个机构",1)]),t("td",ft,[t("span",{class:T(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",D(o.status)])},a(B(o.status)),3)]),t("td",bt,[t("span",{class:T(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",N(o.priority)])},a(L(o.priority)),3)]),t("td",wt,a(j(o.deadline)),1),t("td",kt,[t("div",Ct,[t("div",Mt,[t("div",{class:"bg-blue-600 h-2 rounded-full",style:O({width:`${o.completion_rate}%`})},null,4)]),t("span",$t,a(o.completion_rate)+"%",1)])]),t("td",St,[t("div",Tt,[t("button",{onClick:k=>z(o),class:"text-blue-600 hover:text-blue-900"}," 查看 ",8,Vt),t("button",{onClick:k=>H(o),class:"text-green-600 hover:text-green-900"}," 编辑 ",8,jt),o.status==="draft"?(i(),n("button",{key:0,onClick:k=>Y(o),class:"text-purple-600 hover:text-purple-900"}," 发布 ",8,Bt)):m("",!0)])])])}),128))])]),f.value.length===0?(i(),n("div",Dt,e[12]||(e[12]=[t("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),t("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"暂无统计任务",-1),t("p",{class:"mt-1 text-sm text-gray-500"},"开始创建统计任务来收集数据。",-1)]))):m("",!0)]),r.value.total>r.value.per_page?(i(),n("div",Lt,[t("div",Nt," 显示 "+a(r.value.from)+" 到 "+a(r.value.to)+" 条，共 "+a(r.value.total)+" 条记录 ",1),t("div",zt,[t("button",{onClick:e[3]||(e[3]=o=>x(r.value.current_page-1)),disabled:r.value.current_page<=1,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 上一页 ",8,Ht),t("button",{onClick:e[4]||(e[4]=o=>x(r.value.current_page+1)),disabled:r.value.current_page>=r.value.last_page,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 下一页 ",8,Yt)])])):m("",!0)])])])}}});export{Ft as default};
