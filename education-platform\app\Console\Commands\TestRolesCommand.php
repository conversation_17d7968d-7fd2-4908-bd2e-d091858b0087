<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use App\Models\User;

class TestRolesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:roles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试角色数据';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('检查角色数据...');

        $roles = Role::all();

        $this->info("找到 {$roles->count()} 个角色:");

        foreach ($roles as $role) {
            $this->line("ID: {$role->id}, 名称: {$role->name}, 显示名: {$role->display_name}");
        }

        $this->info('');
        $this->info('检查用户角色关联...');

        $userRoles = \DB::table('model_has_roles')->get();
        $this->info("找到 {$userRoles->count()} 个用户角色关联:");

        foreach ($userRoles as $userRole) {
            $user = User::find($userRole->model_id);
            $role = Role::find($userRole->role_id);
            $this->line("用户: {$user->name} -> 角色: {$role->display_name}");
        }

        return 0;
    }
}
