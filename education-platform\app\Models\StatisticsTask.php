<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StatisticsTask extends Model
{
    protected $fillable = [
        'title',
        'form_template_id',
        'creator_id',
        'target_organizations',
        'start_time',
        'end_time',
        'status',
        'priority',
        'auto_reminder',
        'completion_rate',
        'instructions',
        'attachments'
    ];

    protected $casts = [
        'target_organizations' => 'array',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'auto_reminder' => 'boolean',
        'completion_rate' => 'decimal:2',
        'attachments' => 'array'
    ];

    /**
     * 表单模板
     */
    public function formTemplate(): BelongsTo
    {
        return $this->belongsTo(FormTemplate::class);
    }

    /**
     * 创建者
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * 数据提交记录
     */
    public function dataSubmissions(): HasMany
    {
        return $this->hasMany(DataSubmission::class, 'task_id');
    }

    /**
     * 获取目标组织
     */
    public function getTargetOrganizationsModels()
    {
        if (empty($this->target_organizations)) {
            return collect();
        }

        return Organization::whereIn('id', $this->target_organizations)->get();
    }

    /**
     * 计算完成率
     */
    public function calculateCompletionRate(): float
    {
        $totalTargets = count($this->target_organizations ?? []);

        if ($totalTargets === 0) {
            return 0;
        }

        $completedCount = $this->dataSubmissions()
            ->whereIn('status', ['approved'])
            ->distinct('organization_id')
            ->count('organization_id');

        $rate = ($completedCount / $totalTargets) * 100;

        // 更新完成率
        $this->update(['completion_rate' => $rate]);

        return $rate;
    }

    /**
     * 检查是否已过期
     */
    public function isExpired(): bool
    {
        return $this->end_time && $this->end_time->isPast();
    }

    /**
     * 获取未提交的组织
     */
    public function getPendingOrganizations()
    {
        $submittedOrgIds = $this->dataSubmissions()
            ->whereIn('status', ['submitted', 'approved'])
            ->pluck('organization_id')
            ->toArray();

        $targetOrgIds = array_diff($this->target_organizations ?? [], $submittedOrgIds);

        return Organization::whereIn('id', $targetOrgIds)->get();
    }

    /**
     * 发布任务
     */
    public function publish(): bool
    {
        if ($this->status === 'draft') {
            return $this->update(['status' => 'published']);
        }

        return false;
    }
}
