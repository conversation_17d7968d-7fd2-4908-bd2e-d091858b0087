# 表单构建器功能说明

## 概述

教育数据统计管理平台的表单构建器是一个功能强大的可视化表单设计工具，允许用户通过拖拽方式创建动态表单模板。

## 主要功能

### 1. 表单模板管理
- **创建模板**: 支持创建新的表单模板
- **编辑模板**: 修改现有模板（仅限草稿状态或未使用的模板）
- **查看模板**: 只读模式查看模板详情
- **复制模板**: 快速复制现有模板创建新版本
- **删除模板**: 删除不需要的模板（仅限草稿状态）

### 2. 支持的字段类型
- **文本输入框** (text): 单行文本输入
- **多行文本框** (textarea): 多行文本输入
- **数字输入框** (number): 数字输入，支持最小值、最大值、步长设置
- **下拉选择框** (select): 单选下拉列表
- **复选框组** (checkbox): 多选复选框
- **单选框组** (radio): 单选按钮组
- **日期选择器** (date): 日期输入
- **文件上传** (file): 文件上传，支持文件类型和大小限制

### 3. 字段属性配置
每个字段都支持以下基本属性：
- **字段标签**: 显示给用户的字段名称
- **字段描述**: 可选的字段说明文本
- **占位符**: 输入提示文本
- **是否必填**: 设置字段为必填或可选
- **验证规则**: 根据字段类型设置相应的验证规则

### 4. 验证规则
- **文本字段**: 最小长度、最大长度
- **数字字段**: 最小值、最大值、精度
- **文件字段**: 文件类型、文件大小、是否支持多文件
- **选择字段**: 选项配置

### 5. 表单预览
- **实时预览**: 在设计过程中实时查看表单效果
- **全屏预览**: 模态窗口中查看完整表单
- **字段统计**: 显示表单中各类型字段的数量

## 技术架构

### 前端技术栈
- **Vue 3.4+**: 使用Composition API
- **TypeScript 5.0+**: 类型安全
- **Tailwind CSS 3.x**: 样式框架
- **Vue Router 4.2+**: 路由管理
- **Pinia 2.1+**: 状态管理

### 后端技术栈
- **Laravel 12.x**: PHP框架
- **MySQL 8.0+**: 数据库
- **Laravel Sanctum**: API认证
- **Spatie Laravel Permission**: 权限管理

### 核心组件

#### 前端组件
1. **Create.vue**: 表单模板创建页面
2. **Edit.vue**: 表单模板编辑页面
3. **View.vue**: 表单模板查看页面
4. **Index.vue**: 表单模板列表页面
5. **FormFieldEditor.vue**: 字段编辑器组件
6. **FieldPropertyEditor.vue**: 字段属性编辑器
7. **FormPreviewModal.vue**: 表单预览模态窗口
8. **FormFieldPreview.vue**: 字段预览组件

#### 后端API
- `GET /api/v1/form-templates`: 获取表单模板列表
- `POST /api/v1/form-templates`: 创建新表单模板
- `GET /api/v1/form-templates/{id}`: 获取指定表单模板
- `PUT /api/v1/form-templates/{id}`: 更新表单模板
- `DELETE /api/v1/form-templates/{id}`: 删除表单模板
- `POST /api/v1/form-templates/{id}/duplicate`: 复制表单模板

## 数据结构

### 表单模板数据结构
```json
{
  "id": 1,
  "name": "学生信息统计表",
  "category": "student",
  "description": "用于收集学生基本信息",
  "config": {
    "fields": [
      {
        "id": "field_1",
        "type": "text",
        "label": "姓名",
        "required": true,
        "placeholder": "请输入姓名",
        "validation": {
          "minLength": 2,
          "maxLength": 50
        }
      }
    ]
  },
  "status": "draft",
  "creator": {
    "id": 1,
    "name": "管理员"
  },
  "organization": {
    "id": 1,
    "name": "县教育局"
  }
}
```

## 使用流程

### 1. 创建表单模板
1. 访问表单模板管理页面
2. 点击"创建模板"按钮
3. 填写模板基本信息（名称、分类、描述）
4. 从字段库中拖拽字段到设计区域
5. 配置字段属性和验证规则
6. 预览表单效果
7. 保存模板

### 2. 编辑表单模板
1. 在模板列表中选择要编辑的模板
2. 点击"编辑"按钮（仅限草稿状态）
3. 修改模板信息或字段配置
4. 保存更改

### 3. 使用表单模板
1. 在统计任务中选择表单模板
2. 发布统计任务
3. 目标组织填写表单数据
4. 收集和分析数据

## 权限控制

- **创建权限**: 具有相应权限的用户可以创建表单模板
- **编辑权限**: 只有模板创建者或管理员可以编辑模板
- **状态限制**: 只有草稿状态或未被使用的模板可以编辑/删除
- **组织隔离**: 用户只能管理自己组织的模板

## 测试覆盖

项目包含完整的功能测试，覆盖以下场景：
- 表单模板的CRUD操作
- 权限验证
- 数据验证
- 模板复制功能
- API响应格式验证

运行测试：
```bash
php artisan test --filter=FormTemplateTest
```

## 部署说明

### 前端构建
```bash
npm run build
```

### 数据库迁移
```bash
php artisan migrate --seed
```

### 启动服务
```bash
# 后端服务
php artisan serve

# 前端开发服务（可选）
npm run dev
```

## 扩展性

表单构建器设计时考虑了扩展性：
- 可以轻松添加新的字段类型
- 支持自定义验证规则
- 可以扩展字段属性配置
- 支持主题定制
- 可以集成第三方组件

## 注意事项

1. 表单模板一旦被统计任务使用，就不能随意修改
2. 删除模板前请确保没有相关的统计任务
3. 复制模板时会创建新的草稿版本
4. 字段ID在模板内必须唯一
5. 建议定期备份重要的表单模板
