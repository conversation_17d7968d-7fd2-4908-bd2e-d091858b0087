<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Organization;
use App\Models\FormTemplate;
use App\Models\StatisticsTask;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Sanctum\Sanctum;

class StatisticsTaskTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 运行种子数据
        $this->seed();
    }

    public function test_can_list_statistics_tasks()
    {
        // 创建用户并认证
        $user = User::first();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/v1/statistics-tasks');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                    'meta' => [
                        'current_page',
                        'last_page',
                        'per_page',
                        'total'
                    ]
                ]);
    }

    public function test_can_create_statistics_task()
    {
        $user = User::first();
        Sanctum::actingAs($user);

        $formTemplate = FormTemplate::first();
        $organization = Organization::where('type', 'school')->first();

        $taskData = [
            'title' => '测试统计任务',
            'form_template_id' => $formTemplate->id,
            'target_organizations' => [$organization->id],
            'start_time' => now()->addHour()->format('Y-m-d H:i:s'),
            'end_time' => now()->addDays(7)->format('Y-m-d H:i:s'),
            'priority' => 'normal',
            'instructions' => '请按时完成数据填报'
        ];

        $response = $this->postJson('/api/v1/statistics-tasks', $taskData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'title',
                        'status',
                        'priority'
                    ]
                ]);

        $this->assertDatabaseHas('statistics_tasks', [
            'title' => '测试统计任务',
            'status' => 'draft',
            'priority' => 'normal'
        ]);
    }

    public function test_can_show_statistics_task()
    {
        $user = User::first();
        Sanctum::actingAs($user);

        $task = StatisticsTask::first();

        $response = $this->getJson("/api/v1/statistics-tasks/{$task->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'title',
                        'status',
                        'priority'
                    ]
                ]);
    }

    public function test_can_update_statistics_task()
    {
        $user = User::first();
        Sanctum::actingAs($user);

        // 创建一个draft状态的任务用于测试
        $task = StatisticsTask::create([
            'title' => '测试任务',
            'form_template_id' => FormTemplate::first()->id,
            'status' => 'draft',
            'priority' => 'normal',
            'start_time' => now()->addHour(),
            'end_time' => now()->addDays(7),
            'creator_id' => $user->id,
            'organization_id' => $user->organization_id,
            'target_organizations' => json_encode([Organization::first()->id])
        ]);
        
        $updateData = [
            'title' => '更新后的任务',
            'priority' => 'high',
            'start_time' => now()->addHour()->format('Y-m-d H:i:s'),
            'end_time' => now()->addDays(10)->format('Y-m-d H:i:s'),
            'target_organizations' => [Organization::first()->id]
        ];

        $response = $this->putJson("/api/v1/statistics-tasks/{$task->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('statistics_tasks', [
            'id' => $task->id,
            'title' => '更新后的任务',
            'priority' => 'high'
        ]);
    }

    public function test_can_delete_statistics_task()
    {
        $user = User::first();
        Sanctum::actingAs($user);

        // 创建一个draft状态的任务用于测试
        $task = StatisticsTask::create([
            'title' => '测试删除任务',
            'form_template_id' => FormTemplate::first()->id,
            'status' => 'draft',
            'priority' => 'normal',
            'start_time' => now()->addHour(),
            'end_time' => now()->addDays(7),
            'creator_id' => $user->id,
            'organization_id' => $user->organization_id,
            'target_organizations' => json_encode([Organization::first()->id])
        ]);

        $response = $this->deleteJson("/api/v1/statistics-tasks/{$task->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('statistics_tasks', [
            'id' => $task->id
        ]);
    }

    public function test_can_publish_statistics_task()
    {
        $user = User::first();
        Sanctum::actingAs($user);

        // 创建一个draft状态的任务
        $task = StatisticsTask::create([
            'title' => '测试发布任务',
            'form_template_id' => FormTemplate::first()->id,
            'status' => 'draft',
            'priority' => 'normal',
            'start_time' => now()->addHour(),
            'end_time' => now()->addDays(7),
            'creator_id' => $user->id,
            'organization_id' => $user->organization_id,
            'target_organizations' => json_encode([Organization::first()->id])
        ]);

        $response = $this->postJson("/api/v1/statistics-tasks/{$task->id}/publish");

        $response->assertStatus(200);

        $this->assertDatabaseHas('statistics_tasks', [
            'id' => $task->id,
            'status' => 'published'
        ]);
    }

    public function test_can_get_task_progress()
    {
        $user = User::first();
        Sanctum::actingAs($user);

        $task = StatisticsTask::first();

        $response = $this->getJson("/api/v1/statistics-tasks/{$task->id}/progress");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data'
                ]);
    }

    public function test_requires_authentication()
    {
        $response = $this->getJson('/api/v1/statistics-tasks');
        $response->assertStatus(401);
    }

    public function test_validates_required_fields_on_create()
    {
        $user = User::first();
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/v1/statistics-tasks', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['title', 'form_template_id', 'start_time', 'end_time']);
    }

    public function test_validates_date_range()
    {
        $user = User::first();
        Sanctum::actingAs($user);

        $invalidData = [
            'title' => '测试任务',
            'form_template_id' => FormTemplate::first()->id,
            'target_organizations' => [Organization::first()->id],
            'start_time' => now()->addDays(7)->format('Y-m-d H:i:s'),
            'end_time' => now()->format('Y-m-d H:i:s'), // 结束时间早于开始时间
            'priority' => 'normal'
        ];

        $response = $this->postJson('/api/v1/statistics-tasks', $invalidData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['end_time']);
    }

    public function test_cannot_edit_published_task()
    {
        $user = User::first();
        Sanctum::actingAs($user);

        // 创建一个已发布的任务
        $task = StatisticsTask::create([
            'title' => '已发布任务',
            'form_template_id' => FormTemplate::first()->id,
            'status' => 'published',
            'priority' => 'normal',
            'start_time' => now()->addHour(),
            'end_time' => now()->addDays(7),
            'creator_id' => $user->id,
            'organization_id' => $user->organization_id,
            'target_organizations' => json_encode([Organization::first()->id])
        ]);

        $updateData = [
            'title' => '尝试更新已发布任务'
        ];

        $response = $this->putJson("/api/v1/statistics-tasks/{$task->id}", $updateData);

        $response->assertStatus(422);
    }
}
