<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('statistics_tasks', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('任务标题');
            $table->unsignedBigInteger('form_template_id')->comment('表单模板ID');
            $table->unsignedBigInteger('creator_id')->comment('创建者ID');
            $table->json('target_organizations')->comment('目标组织列表');
            $table->timestamp('start_time')->nullable()->comment('开始时间');
            $table->timestamp('end_time')->nullable()->comment('结束时间');
            $table->enum('status', ['draft', 'published', 'in_progress', 'completed', 'cancelled'])->default('draft')->comment('状态');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal')->comment('优先级');
            $table->boolean('auto_reminder')->default(true)->comment('自动提醒');
            $table->decimal('completion_rate', 5, 2)->default(0.00)->comment('完成率');
            $table->text('instructions')->nullable()->comment('填报说明');
            $table->json('attachments')->nullable()->comment('附件');
            $table->timestamps();

            // 索引
            $table->index(['status', 'end_time']);
            $table->index('creator_id');
            $table->index('form_template_id');

            // 外键约束
            $table->foreign('form_template_id')->references('id')->on('form_templates')->onDelete('cascade');
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('statistics_tasks');
    }
};
