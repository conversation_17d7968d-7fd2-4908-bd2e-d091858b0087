import { createRouter, createWebHistory } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/organizations',
    name: 'Organizations',
    component: () => import('@/views/organizations/Index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/views/users/Index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/form-templates',
    name: 'FormTemplates',
    component: () => import('@/views/form-templates/Index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/form-templates/create',
    name: 'FormTemplateCreate',
    component: () => import('@/views/form-templates/Create.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/form-templates/:id/edit',
    name: 'FormTemplateEdit',
    component: () => import('@/views/form-templates/Edit.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/form-templates/:id',
    name: 'FormTemplateView',
    component: () => import('@/views/form-templates/View.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/statistics-tasks',
    name: 'StatisticsTasks',
    component: () => import('@/views/statistics-tasks/Index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/statistics-tasks/create',
    name: 'StatisticsTaskCreate',
    component: () => import('@/views/statistics-tasks/Create.vue'),
    meta: { requiresAuth: true }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login');
    return;
  }
  
  // 检查是否需要游客状态
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next('/dashboard');
    return;
  }
  
  next();
});

export default router;
