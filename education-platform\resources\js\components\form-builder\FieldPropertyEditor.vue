<template>
  <div class="space-y-4">
    <!-- 基本属性 -->
    <div>
      <h4 class="text-sm font-medium text-gray-900 mb-3">基本属性</h4>
      
      <div class="space-y-3">
        <!-- 字段标签 -->
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">字段标签</label>
          <input
            v-model="localField.label"
            type="text"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            @input="updateField"
          />
        </div>

        <!-- 占位符 -->
        <div v-if="showPlaceholder">
          <label class="block text-xs font-medium text-gray-700 mb-1">占位符</label>
          <input
            v-model="localField.placeholder"
            type="text"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            @input="updateField"
          />
        </div>

        <!-- 字段描述 -->
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">字段描述</label>
          <textarea
            v-model="localField.description"
            rows="2"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            @input="updateField"
          ></textarea>
        </div>

        <!-- 必填项 -->
        <div class="flex items-center">
          <input
            v-model="localField.required"
            type="checkbox"
            class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            @change="updateField"
          />
          <label class="ml-2 text-xs text-gray-700">必填项</label>
        </div>
      </div>
    </div>

    <!-- 选项配置（适用于 select, checkbox, radio） -->
    <div v-if="hasOptions" class="border-t pt-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">选项配置</h4>
      
      <div class="space-y-2">
        <div
          v-for="(option, index) in localField.options"
          :key="index"
          class="flex items-center space-x-2"
        >
          <input
            v-model="localField.options![index]"
            type="text"
            class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            @input="updateField"
          />
          <button
            @click="removeOption(index)"
            class="p-1 text-red-400 hover:text-red-600"
            title="删除选项"
          >
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <button
          @click="addOption"
          class="w-full px-2 py-1 text-sm text-blue-600 border border-blue-300 border-dashed rounded hover:bg-blue-50"
        >
          + 添加选项
        </button>
      </div>
    </div>

    <!-- 数字字段配置 -->
    <div v-if="field.type === 'number'" class="border-t pt-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">数字配置</h4>
      
      <div class="space-y-3">
        <div class="grid grid-cols-2 gap-2">
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">最小值</label>
            <input
              v-model.number="localField.validation.min"
              type="number"
              class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
              @input="updateField"
            />
          </div>
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">最大值</label>
            <input
              v-model.number="localField.validation.max"
              type="number"
              class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
              @input="updateField"
            />
          </div>
        </div>
        
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">小数位数</label>
          <select
            v-model.number="localField.validation.precision"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            @change="updateField"
          >
            <option :value="0">整数</option>
            <option :value="1">1位小数</option>
            <option :value="2">2位小数</option>
            <option :value="3">3位小数</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 文本字段配置 -->
    <div v-if="['text', 'textarea'].includes(field.type)" class="border-t pt-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">文本配置</h4>
      
      <div class="space-y-3">
        <div class="grid grid-cols-2 gap-2">
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">最小长度</label>
            <input
              v-model.number="localField.validation.minLength"
              type="number"
              min="0"
              class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
              @input="updateField"
            />
          </div>
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">最大长度</label>
            <input
              v-model.number="localField.validation.maxLength"
              type="number"
              min="1"
              class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
              @input="updateField"
            />
          </div>
        </div>

        <!-- 文本区域行数 -->
        <div v-if="field.type === 'textarea'">
          <label class="block text-xs font-medium text-gray-700 mb-1">显示行数</label>
          <input
            v-model.number="localField.rows"
            type="number"
            min="1"
            max="10"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            @input="updateField"
          />
        </div>
      </div>
    </div>

    <!-- 文件上传配置 -->
    <div v-if="field.type === 'file'" class="border-t pt-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">文件配置</h4>
      
      <div class="space-y-3">
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">允许的文件类型</label>
          <input
            v-model="localField.validation.accept"
            type="text"
            placeholder="例如: .jpg,.png,.pdf"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            @input="updateField"
          />
        </div>
        
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">最大文件大小 (MB)</label>
          <input
            v-model.number="localField.validation.maxSize"
            type="number"
            min="1"
            max="100"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            @input="updateField"
          />
        </div>

        <div class="flex items-center">
          <input
            v-model="localField.validation.multiple"
            type="checkbox"
            class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            @change="updateField"
          />
          <label class="ml-2 text-xs text-gray-700">允许多文件上传</label>
        </div>
      </div>
    </div>

    <!-- 高级配置 -->
    <div class="border-t pt-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">高级配置</h4>
      
      <div class="space-y-3">
        <!-- 字段ID -->
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">字段ID</label>
          <input
            v-model="localField.id"
            type="text"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-gray-50"
            readonly
          />
        </div>

        <!-- 字段类型 -->
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">字段类型</label>
          <input
            :value="getFieldTypeLabel(field.type)"
            type="text"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-gray-50"
            readonly
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

interface FormField {
  id: string;
  type: string;
  label: string;
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: any;
  description?: string;
  rows?: number;
}

interface Props {
  field: FormField;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  update: [field: FormField];
}>();

// 计算属性
const hasOptions = computed(() => {
  return ['select', 'checkbox', 'radio'].includes(props.field.type);
});

// 创建本地副本以避免直接修改 props
const localField = ref<FormField>({ ...props.field });

// 初始化验证对象
if (!localField.value.validation) {
  localField.value.validation = {};
}

// 初始化选项数组
if (!localField.value.options && hasOptions.value) {
  localField.value.options = ['选项1', '选项2'];
}

// 监听 props 变化
watch(() => props.field, (newField) => {
  localField.value = { ...newField };
  if (!localField.value.validation) {
    localField.value.validation = {};
  }
}, { deep: true });

const showPlaceholder = computed(() => {
  return ['text', 'textarea', 'number'].includes(props.field.type);
});

// 方法
const updateField = () => {
  emit('update', { ...localField.value });
};

const addOption = () => {
  if (!localField.value.options) {
    localField.value.options = [];
  }
  localField.value.options.push(`选项${localField.value.options.length + 1}`);
  updateField();
};

const removeOption = (index: number) => {
  if (localField.value.options && localField.value.options.length > 1) {
    localField.value.options.splice(index, 1);
    updateField();
  }
};

const getFieldTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    text: '单行文本',
    textarea: '多行文本',
    number: '数字',
    select: '下拉选择',
    checkbox: '多选框',
    radio: '单选框',
    date: '日期',
    file: '文件上传'
  };
  return typeMap[type] || type;
};
</script>
