import{d as Z,u as ee,r as m,m as F,n as te,g as se,c as d,a as e,b as v,i as oe,j as ae,k as ne,t as i,h as le,e as _,v as S,F as $,p as z,w as ie,q as V,l as E,s as w,f as re,o as u,x as A}from"./app-D0Qwllno.js";const de={class:"min-h-screen bg-gray-50"},ue={class:"bg-white shadow"},ce={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},me={class:"flex justify-between h-16"},pe={class:"flex items-center space-x-4"},xe={class:"flex items-center space-x-4"},fe={class:"text-sm text-gray-700"},ge={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},ve={class:"px-4 py-6 sm:px-0"},be={class:"mb-6 flex justify-between items-center"},ye={class:"flex space-x-4"},he=["disabled"],_e={class:"flex items-center space-x-2"},we={class:"bg-white shadow overflow-hidden sm:rounded-md"},ke={key:0,class:"p-6 text-center"},Ce={key:1,class:"divide-y divide-gray-200"},Te={class:"flex items-center justify-between"},Ee={class:"flex items-center"},De={class:"flex-shrink-0"},Oe={class:"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center"},Se={class:"text-sm font-medium text-gray-700"},$e={class:"ml-4"},ze={class:"flex items-center"},Ie={class:"text-sm font-medium text-gray-900"},Le={class:"text-sm text-gray-500"},Me={key:0,class:"text-xs text-gray-400"},Re={class:"flex items-center space-x-2"},Ue=["onClick"],je=["onClick"],Ne={key:2,class:"p-6 text-center"},Fe={key:0,class:"mt-6 flex items-center justify-between"},Ve={class:"text-sm text-gray-700"},Ae={class:"flex space-x-2"},He=["disabled"],qe=["disabled"],Be={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ge={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},Pe={class:"mt-3"},Ke={class:"text-lg font-medium text-gray-900 mb-4"},Qe={class:"space-y-4"},We={class:"flex gap-2"},Ye={class:"mt-2 text-xs text-gray-400"},Je={class:"mt-1 max-h-20 overflow-y-auto bg-gray-50 p-2 rounded"},Xe={key:0,class:"text-xs text-gray-400"},Ze={key:0},et=["value"],tt={class:"mt-1 text-xs text-gray-500"},st={class:"mt-6 flex justify-end space-x-3"},ot=["disabled"],at={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},nt={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},lt={class:"mt-3"},it={class:"mb-4"},rt={class:"flex justify-end space-x-3"},dt=["disabled"],mt=Z({__name:"Index",setup(ut){const H=re(),I=ee(),p=m([]),k=m(!1),C=m(!1),b=m(""),g=m(!1),D=m(!1),O=m(!1),y=m(null),h=m(null),T=m(!1),x=m({current_page:1,last_page:1,per_page:15,total:0,from:0,to:0}),n=m({name:"",code:"",type:"school",parent_id:""}),L=F(()=>b.value?p.value.filter(a=>a.name.toLowerCase().includes(b.value.toLowerCase())||a.code.toLowerCase().includes(b.value.toLowerCase())):p.value),M=F(()=>{if(!n.value.type)return[];const t={county:[],district:["county"],school:["district"],kindergarten:["district"]}[n.value.type]||[];return t.length===0?[]:p.value.filter(l=>{var o;return t.includes(l.type)&&l.id!==((o=y.value)==null?void 0:o.id)})}),f=async(a=1)=>{var t,l;k.value=!0;try{const o=await w.get(`/organizations?page=${a}`);o.data.success&&o.data.data?o.data.data.data?(p.value=o.data.data.data||[],x.value={current_page:o.data.data.current_page,last_page:o.data.data.last_page,per_page:o.data.data.per_page,total:o.data.data.total,from:o.data.data.from,to:o.data.data.to}):Array.isArray(o.data.data)?p.value=o.data.data:p.value=[]:p.value=[]}catch(o){console.error("加载组织列表失败:",o),alert("加载组织失败: "+(((l=(t=o.response)==null?void 0:t.data)==null?void 0:l.message)||o.message))}finally{k.value=!1}},R=a=>({county:"县/区教育局",district:"学区中心校",school:"学校",kindergarten:"幼儿园"})[a]||a,q=a=>({county:"bg-purple-100 text-purple-800",district:"bg-blue-100 text-blue-800",school:"bg-green-100 text-green-800",kindergarten:"bg-yellow-100 text-yellow-800"})[a]||"bg-gray-100 text-gray-800",B=a=>{y.value=a,n.value={name:a.name,code:a.code,type:a.type,parent_id:a.parent_id||""},D.value=!0},G=async a=>{if(confirm(`确定要删除组织"${a.name}"吗？`))try{await w.delete(`/organizations/${a.id}`),await f()}catch(t){console.error("删除组织失败:",t),alert("删除失败")}},P=()=>{n.value.parent_id=""},K=()=>({district:"学区中心校应隶属于县/区教育局",school:"学校应隶属于学区中心校",kindergarten:"幼儿园应隶属于学区中心校"})[n.value.type]||"",Q=async()=>{var a,t,l,o;C.value=!0;try{if(g.value)await w.post("/organizations",n.value);else if((a=y.value)!=null&&a.id)await w.put(`/organizations/${y.value.id}`,n.value);else throw new Error("无效的编辑状态");await f(),j()}catch(s){console.error("保存失败:",s);let r="保存失败";if(((t=s.response)==null?void 0:t.status)===422){const c=s.response.data.errors;c!=null&&c.code?r=`组织编码 "${n.value.code}" 已被使用，请使用其他编码`:c!=null&&c.name?r=`组织名称 "${n.value.name}" 已被使用，请使用其他名称`:r=s.response.data.message||"数据验证失败"}else(o=(l=s.response)==null?void 0:l.data)!=null&&o.message?r=s.response.data.message:s.message&&(r=s.message);alert(r)}finally{C.value=!1}},U=()=>{const a=n.value.type;let t="";switch(a){case"school":t="SCHOOL_";break;case"district":t="EDU_DISTRICT_";break;case"kindergarten":t="KINDERGARTEN_";break;case"county":t="EDU_COUNTY_";break;default:t="ORG_"}const l=(p.value||[]).filter(r=>{var c;return(c=r==null?void 0:r.code)==null?void 0:c.startsWith(t)}).map(r=>{const c=r.code.match(new RegExp(`${t}(\\d+)`));return c?parseInt(c[1]):0}),o=l.length>0?Math.max(...l):0,s=String(o+1).padStart(3,"0");n.value.code=t+s},j=()=>{g.value=!1,D.value=!1,y.value=null,n.value={name:"",code:"",type:"school",parent_id:""}},W=()=>{const t=[["名称","编码","类型","父级编码"],["示例学区","130182999","district","130182000"],["示例学校","130182998","school","130182999"]].map(r=>r.join(",")).join(`
`),l=new Blob(["\uFEFF"+t],{type:"text/csv;charset=utf-8;"}),o=document.createElement("a"),s=URL.createObjectURL(l);o.setAttribute("href",s),o.setAttribute("download","组织导入模板.csv"),o.style.visibility="hidden",document.body.appendChild(o),o.click(),document.body.removeChild(o)},Y=a=>{const t=a.target;t.files&&t.files.length>0&&(h.value=t.files[0])},N=()=>{O.value=!1,h.value=null},J=async()=>{var a,t;if(h.value){T.value=!0;try{const l=new FormData;l.append("file",h.value);const o=await w.post("/organizations/import",l,{headers:{"Content-Type":"multipart/form-data"}});o.data.success?(alert(`导入成功！共导入 ${o.data.imported_count} 个组织`),await f(),N()):alert("导入失败："+(o.data.message||"未知错误"))}catch(l){console.error("导入失败:",l),alert("导入失败："+(((t=(a=l.response)==null?void 0:a.data)==null?void 0:t.message)||l.message||"网络错误"))}finally{T.value=!1}}},X=async()=>{await I.logout(),H.push("/login")};return te(()=>n.value.type,()=>{g.value&&!n.value.code&&U()}),se(()=>{f()}),(a,t)=>{var o;const l=ne("router-link");return u(),d("div",de,[e("nav",ue,[e("div",ce,[e("div",me,[e("div",pe,[oe(l,{to:"/dashboard",class:"text-indigo-600 hover:text-indigo-500"},{default:ae(()=>t[9]||(t[9]=[E(" ← 返回首页 ",-1)])),_:1,__:[9]}),t[10]||(t[10]=e("h1",{class:"text-xl font-semibold text-gray-900"}," 组织架构管理 ",-1))]),e("div",xe,[e("span",fe,i((o=le(I).user)==null?void 0:o.name),1),e("button",{onClick:X,class:"text-sm text-gray-500 hover:text-gray-700"}," 退出登录 ")])])])]),e("div",ge,[e("div",ve,[e("div",be,[e("div",ye,[e("button",{onClick:t[0]||(t[0]=s=>g.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"}," 新增组织 "),e("button",{onClick:W,class:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"}," 下载模板 "),e("button",{onClick:t[1]||(t[1]=s=>O.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," 批量导入 "),e("button",{onClick:f,disabled:k.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"}," 刷新 ",8,he)]),e("div",_e,[_(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>b.value=s),type:"text",placeholder:"搜索组织...",class:"block w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"},null,512),[[S,b.value]])])]),e("div",we,[k.value?(u(),d("div",ke,t[11]||(t[11]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"加载中...",-1)]))):L.value.length>0?(u(),d("ul",Ce,[(u(!0),d($,null,z(L.value,s=>(u(),d("li",{key:s.id,class:"px-6 py-4"},[e("div",Te,[e("div",Ee,[e("div",De,[e("div",Oe,[e("span",Se,i(s.name.charAt(0)),1)])]),e("div",$e,[e("div",ze,[e("p",Ie,i(s.name),1),e("span",{class:A(["ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",q(s.type)])},i(R(s.type)),3)]),e("p",Le," 编码: "+i(s.code)+" | 层级: "+i(s.level),1),s.parent?(u(),d("p",Me," 上级: "+i(s.parent.name),1)):v("",!0)])]),e("div",Re,[e("span",{class:A(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},i(s.status?"启用":"禁用"),3),e("button",{onClick:r=>B(s),class:"text-indigo-600 hover:text-indigo-500 text-sm"}," 编辑 ",8,Ue),e("button",{onClick:r=>G(s),class:"text-red-600 hover:text-red-500 text-sm"}," 删除 ",8,je)])])]))),128))])):(u(),d("div",Ne,t[12]||(t[12]=[e("p",{class:"text-sm text-gray-500"},"暂无组织数据",-1)])))]),x.value.total>x.value.per_page?(u(),d("div",Fe,[e("div",Ve," 显示 "+i(x.value.from)+" 到 "+i(x.value.to)+" 条，共 "+i(x.value.total)+" 条记录 ",1),e("div",Ae,[e("button",{onClick:t[3]||(t[3]=s=>f(x.value.current_page-1)),disabled:x.value.current_page<=1,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 上一页 ",8,He),e("button",{onClick:t[4]||(t[4]=s=>f(x.value.current_page+1)),disabled:x.value.current_page>=x.value.last_page,class:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," 下一页 ",8,qe)])])):v("",!0)])]),g.value||D.value?(u(),d("div",Be,[e("div",Ge,[e("div",Pe,[e("h3",Ke,i(g.value?"新增组织":"编辑组织"),1),e("form",{onSubmit:ie(Q,["prevent"])},[e("div",Qe,[e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700"},"组织名称",-1)),_(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>n.value.name=s),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"},null,512),[[S,n.value.name]])]),e("div",null,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700"},"组织编码",-1)),e("div",We,[_(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>n.value.code=s),type:"text",required:"",placeholder:"例如：SCHOOL_004, EDU_DISTRICT_003",class:"flex-1 mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"},null,512),[[S,n.value.code]]),e("button",{type:"button",onClick:U,class:"mt-1 px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"}," 生成 ")]),t[16]||(t[16]=e("p",{class:"mt-1 text-xs text-gray-500"}," 组织编码必须唯一，建议格式：学校用SCHOOL_xxx，学区用EDU_DISTRICT_xxx，幼儿园用KINDERGARTEN_xxx ",-1)),e("div",Ye,[e("details",null,[t[14]||(t[14]=e("summary",{class:"cursor-pointer hover:text-gray-600"},"查看已使用的编码",-1)),e("div",Je,[(u(!0),d($,null,z(p.value||[],s=>(u(),d("div",{key:(s==null?void 0:s.id)||Math.random(),class:"text-xs"},i(s==null?void 0:s.code)+" - "+i(s==null?void 0:s.name),1))),128)),!p.value||p.value.length===0?(u(),d("div",Xe," 暂无数据 ")):v("",!0)])])])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700"},"组织类型",-1)),_(e("select",{"onUpdate:modelValue":t[7]||(t[7]=s=>n.value.type=s),required:"",onChange:P,class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"},t[17]||(t[17]=[e("option",{value:"county"},"县/区教育局",-1),e("option",{value:"district"},"学区中心校",-1),e("option",{value:"school"},"学校",-1),e("option",{value:"kindergarten"},"幼儿园",-1)]),544),[[V,n.value.type]])]),M.value.length>0?(u(),d("div",Ze,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700"},"上级组织",-1)),_(e("select",{"onUpdate:modelValue":t[8]||(t[8]=s=>n.value.parent_id=s),class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"},[t[19]||(t[19]=e("option",{value:""},"请选择上级组织",-1)),(u(!0),d($,null,z(M.value,s=>(u(),d("option",{key:s.id,value:s.id},i(s.name)+" ("+i(R(s.type))+") ",9,et))),128))],512),[[V,n.value.parent_id]]),e("p",tt,i(K()),1)])):v("",!0)]),e("div",st,[e("button",{type:"button",onClick:j,class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"}," 取消 "),e("button",{type:"submit",disabled:C.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"},i(C.value?"保存中...":"保存"),9,ot)])],32)])])])):v("",!0),O.value?(u(),d("div",at,[e("div",nt,[e("div",lt,[t[22]||(t[22]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"批量导入组织",-1)),e("div",it,[t[21]||(t[21]=e("p",{class:"text-sm text-gray-600 mb-2"},"请选择Excel文件进行批量导入：",-1)),e("input",{type:"file",accept:".xlsx,.xls",onChange:Y,class:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"},null,32)]),t[23]||(t[23]=e("div",{class:"mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md"},[e("p",{class:"text-sm text-yellow-800"},[e("strong",null,"导入说明："),e("br"),E(" • Excel文件应包含：名称、编码、类型、父级编码列"),e("br"),E(" • 类型可选：county, district, school, kindergarten"),e("br"),E(" • 父级编码为空表示顶级组织 ")])],-1)),e("div",rt,[e("button",{onClick:N,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"}," 取消 "),e("button",{onClick:J,disabled:!h.value||T.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 rounded-md"},i(T.value?"导入中...":"开始导入"),9,dt)])])])])):v("",!0)])}}});export{mt as default};
