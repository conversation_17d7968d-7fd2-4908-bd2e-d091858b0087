<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PermissionController extends Controller
{
    public function index(): JsonResponse
    {
        return response()->json(['success' => true, 'data' => []]);
    }

    public function store(Request $request): JsonResponse
    {
        return response()->json(['success' => true, 'message' => 'Not implemented yet']);
    }

    public function show(string $id): JsonResponse
    {
        return response()->json(['success' => true, 'data' => []]);
    }

    public function update(Request $request, string $id): JsonResponse
    {
        return response()->json(['success' => true, 'message' => 'Not implemented yet']);
    }

    public function destroy(string $id): JsonResponse
    {
        return response()->json(['success' => true, 'message' => 'Not implemented yet']);
    }
}
