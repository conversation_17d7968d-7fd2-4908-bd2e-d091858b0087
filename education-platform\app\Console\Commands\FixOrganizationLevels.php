<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organization;

class FixOrganizationLevels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:organization-levels';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修复组织层级，确保层级与组织类型匹配';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始修复组织层级...');

        // 定义正确的层级映射
        $typeToLevel = [
            'county' => 1,      // 县/区教育局
            'district' => 2,    // 学区中心校
            'school' => 3,      // 学校
            'kindergarten' => 3 // 幼儿园
        ];

        $organizations = Organization::all();
        $fixedCount = 0;

        foreach ($organizations as $org) {
            $correctLevel = $typeToLevel[$org->type] ?? 1;

            if ($org->level != $correctLevel) {
                $this->line("修复组织: {$org->name} (类型: {$org->type}) 从层级 {$org->level} 改为 {$correctLevel}");

                $org->update(['level' => $correctLevel]);
                $fixedCount++;
            }
        }

        $this->info("组织层级修复完成！共修复了 {$fixedCount} 个组织。");

        // 显示修复后的结果
        $this->info("\n修复后的组织结构:");
        $organizations = Organization::orderBy('level')->orderBy('sort_order')->get();

        foreach ($organizations as $org) {
            $indent = str_repeat('  ', $org->level - 1);
            $this->line("{$indent}[L{$org->level}] {$org->name} ({$org->type})");
        }

        return 0;
    }
}
