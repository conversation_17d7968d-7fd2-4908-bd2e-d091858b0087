<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class TestUsersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试用户数据';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('检查用户数据...');

        $users = User::with(['organization', 'roles'])->get();

        $this->info("找到 {$users->count()} 个用户:");

        foreach ($users as $user) {
            $this->line("ID: {$user->id}, 姓名: {$user->name}, 邮箱: {$user->email}, 状态: " . ($user->status ? '启用' : '禁用'));
            $this->line("  组织: " . ($user->organization ? $user->organization->name : '无'));
            $this->line("  角色: " . $user->roles->pluck('display_name')->join(', '));
            $this->line('');
        }

        return 0;
    }
}
