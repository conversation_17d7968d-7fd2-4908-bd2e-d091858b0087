<?php

namespace Database\Seeders;

use App\Models\StatisticsTask;
use App\Models\FormTemplate;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class StatisticsTaskSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('email', '<EMAIL>')->first();
        $formTemplates = FormTemplate::where('status', 'published')->get();
        $schools = Organization::where('type', 'school')->get();

        if ($formTemplates->isEmpty() || $schools->isEmpty()) {
            return; // 如果没有表单模板或学校，跳过创建任务
        }

        $tasks = [
            [
                'title' => '2024年春季学期基础信息统计',
                'form_template_id' => $formTemplates->first()->id,
                'target_organizations' => $schools->take(3)->pluck('id')->toArray(),
                'start_time' => Carbon::now()->addDays(1),
                'end_time' => Carbon::now()->addDays(15),
                'status' => 'published',
                'priority' => 'high',
                'auto_reminder' => true,
                'completion_rate' => 33.33,
                'instructions' => '请各学校认真填写基础信息统计表，确保数据准确性。截止时间为' . Carbon::now()->addDays(15)->format('Y年m月d日') . '。',
                'attachments' => [
                    ['name' => '填报说明.pdf', 'url' => '/attachments/instructions.pdf'],
                    ['name' => '示例表格.xlsx', 'url' => '/attachments/example.xlsx']
                ]
            ],
            [
                'title' => '教师资格证书统计调查',
                'form_template_id' => $formTemplates->count() > 1 ? $formTemplates->get(1)->id : $formTemplates->first()->id,
                'target_organizations' => $schools->take(5)->pluck('id')->toArray(),
                'start_time' => Carbon::now()->subDays(5),
                'end_time' => Carbon::now()->addDays(10),
                'status' => 'in_progress',
                'priority' => 'normal',
                'auto_reminder' => true,
                'completion_rate' => 60.00,
                'instructions' => '本次调查旨在了解各校教师资格证书持有情况，请如实填报。',
                'attachments' => null
            ],
            [
                'title' => '学校安全检查统计',
                'form_template_id' => $formTemplates->first()->id,
                'target_organizations' => $schools->pluck('id')->toArray(),
                'start_time' => Carbon::now()->addDays(7),
                'end_time' => Carbon::now()->addDays(21),
                'status' => 'draft',
                'priority' => 'urgent',
                'auto_reminder' => true,
                'completion_rate' => 0.00,
                'instructions' => '安全检查统计表，涉及校园安全各个方面，请详细填写。',
                'attachments' => [
                    ['name' => '安全检查清单.pdf', 'url' => '/attachments/safety_checklist.pdf']
                ]
            ]
        ];

        foreach ($tasks as $taskData) {
            StatisticsTask::create([
                'title' => $taskData['title'],
                'form_template_id' => $taskData['form_template_id'],
                'creator_id' => $admin->id,
                'target_organizations' => $taskData['target_organizations'],
                'start_time' => $taskData['start_time'],
                'end_time' => $taskData['end_time'],
                'status' => $taskData['status'],
                'priority' => $taskData['priority'],
                'auto_reminder' => $taskData['auto_reminder'],
                'completion_rate' => $taskData['completion_rate'],
                'instructions' => $taskData['instructions'],
                'attachments' => $taskData['attachments']
            ]);
        }
    }
}
