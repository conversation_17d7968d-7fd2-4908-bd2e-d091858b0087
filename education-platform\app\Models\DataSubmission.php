<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DataSubmission extends Model
{
    protected $fillable = [
        'task_id',
        'organization_id',
        'submitter_id',
        'data',
        'status',
        'version',
        'submit_time',
        'review_time',
        'reviewer_id',
        'review_comments',
        'data_hash'
    ];

    protected $casts = [
        'data' => 'array',
        'submit_time' => 'datetime',
        'review_time' => 'datetime'
    ];

    /**
     * 统计任务
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(StatisticsTask::class, 'task_id');
    }

    /**
     * 提交组织
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * 提交者
     */
    public function submitter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'submitter_id');
    }

    /**
     * 审核者
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    /**
     * 提交数据
     */
    public function submit(): bool
    {
        if ($this->status === 'draft') {
            return $this->update([
                'status' => 'submitted',
                'submit_time' => now(),
                'data_hash' => hash('sha256', json_encode($this->data))
            ]);
        }

        return false;
    }

    /**
     * 审核通过
     */
    public function approve(User $reviewer, string $comments = null): bool
    {
        if ($this->status === 'submitted' || $this->status === 'reviewing') {
            return $this->update([
                'status' => 'approved',
                'review_time' => now(),
                'reviewer_id' => $reviewer->id,
                'review_comments' => $comments
            ]);
        }

        return false;
    }

    /**
     * 审核驳回
     */
    public function reject(User $reviewer, string $comments): bool
    {
        if ($this->status === 'submitted' || $this->status === 'reviewing') {
            return $this->update([
                'status' => 'rejected',
                'review_time' => now(),
                'reviewer_id' => $reviewer->id,
                'review_comments' => $comments
            ]);
        }

        return false;
    }

    /**
     * 验证数据完整性
     */
    public function verifyDataIntegrity(): bool
    {
        if (!$this->data_hash) {
            return false;
        }

        return $this->data_hash === hash('sha256', json_encode($this->data));
    }

    /**
     * 检查是否可以编辑
     */
    public function canEdit(): bool
    {
        return in_array($this->status, ['draft', 'rejected']);
    }
}
