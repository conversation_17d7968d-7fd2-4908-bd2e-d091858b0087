<?php

namespace Database\Seeders;

use App\Models\FormTemplate;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Database\Seeder;

class FormTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('email', '<EMAIL>')->first();
        $county = Organization::where('type', 'county')->first();

        $templates = [
            [
                'name' => '学校基础信息统计表',
                'category' => '基础信息',
                'description' => '收集学校基本信息，包括学生数量、教师数量、设施情况等',
                'config' => [
                    'fields' => [
                        [
                            'id' => 'school_name',
                            'type' => 'text',
                            'label' => '学校名称',
                            'required' => true,
                            'validation' => ['maxLength' => 100]
                        ],
                        [
                            'id' => 'student_count',
                            'type' => 'number',
                            'label' => '学生总数',
                            'required' => true,
                            'validation' => ['min' => 0, 'max' => 10000]
                        ],
                        [
                            'id' => 'teacher_count',
                            'type' => 'number',
                            'label' => '教师总数',
                            'required' => true,
                            'validation' => ['min' => 0, 'max' => 1000]
                        ],
                        [
                            'id' => 'grade_levels',
                            'type' => 'checkbox',
                            'label' => '年级设置',
                            'options' => ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级'],
                            'required' => true
                        ],
                        [
                            'id' => 'facilities',
                            'type' => 'textarea',
                            'label' => '设施情况说明',
                            'required' => false
                        ]
                    ]
                ],
                'status' => 'published'
            ],
            [
                'name' => '教师资格统计表',
                'category' => '人事信息',
                'description' => '统计教师资格证书情况和专业背景',
                'config' => [
                    'fields' => [
                        [
                            'id' => 'total_teachers',
                            'type' => 'number',
                            'label' => '教师总数',
                            'required' => true
                        ],
                        [
                            'id' => 'qualified_teachers',
                            'type' => 'number',
                            'label' => '持证教师数',
                            'required' => true
                        ],
                        [
                            'id' => 'education_levels',
                            'type' => 'select',
                            'label' => '学历分布',
                            'options' => ['专科', '本科', '硕士', '博士'],
                            'multiple' => true,
                            'required' => true
                        ]
                    ]
                ],
                'status' => 'published'
            ]
        ];

        foreach ($templates as $templateData) {
            FormTemplate::create([
                'name' => $templateData['name'],
                'category' => $templateData['category'],
                'description' => $templateData['description'],
                'config' => $templateData['config'],
                'status' => $templateData['status'],
                'creator_id' => $admin->id,
                'organization_id' => $county->id,
                'usage_count' => 0
            ]);
        }
    }
}
