import{d as g,u as x,r as i,c as l,a as e,w as y,b as v,e as u,v as p,t as b,o as r,f as w}from"./app-D0Qwllno.js";const h={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},_={class:"max-w-md w-full space-y-8"},k={class:"rounded-md shadow-sm -space-y-px"},S={key:0,class:"text-red-600 text-sm text-center"},V=["disabled"],B={key:0},M={key:1},z=g({__name:"Login",setup(j){const c=w(),m=x(),o=i({email:"",password:""}),t=i(!1),a=i(""),f=async()=>{t.value=!0,a.value="";const n=await m.login(o.value);n.success?c.push("/dashboard"):a.value=n.message||"登录失败",t.value=!1};return(n,s)=>(r(),l("div",h,[e("div",_,[s[5]||(s[5]=e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," 教育数据统计管理平台 "),e("p",{class:"mt-2 text-center text-sm text-gray-600"}," 请登录您的账户 ")],-1)),e("form",{class:"mt-8 space-y-6",onSubmit:y(f,["prevent"])},[e("div",k,[e("div",null,[s[2]||(s[2]=e("label",{for:"email",class:"sr-only"},"邮箱地址",-1)),u(e("input",{id:"email","onUpdate:modelValue":s[0]||(s[0]=d=>o.value.email=d),name:"email",type:"email",autocomplete:"email",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"邮箱地址"},null,512),[[p,o.value.email]])]),e("div",null,[s[3]||(s[3]=e("label",{for:"password",class:"sr-only"},"密码",-1)),u(e("input",{id:"password","onUpdate:modelValue":s[1]||(s[1]=d=>o.value.password=d),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"密码"},null,512),[[p,o.value.password]])])]),a.value?(r(),l("div",S,b(a.value),1)):v("",!0),e("div",null,[e("button",{type:"submit",disabled:t.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"},[t.value?(r(),l("span",B,"登录中...")):(r(),l("span",M,"登录"))],8,V)]),s[4]||(s[4]=e("div",{class:"text-sm text-center text-gray-600"},[e("p",null,"测试账户："),e("p",null,"超级管理员：<EMAIL>"),e("p",null,"教育局管理员：<EMAIL>"),e("p",null,"密码：password")],-1))],32)])]))}});export{z as default};
