<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = User::with(['organization', 'roles']);

        // 搜索过滤
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // 组织过滤
        if ($request->has('organization_id')) {
            $query->where('organization_id', $request->get('organization_id'));
        }

        // 状态过滤
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // 角色过滤
        if ($request->has('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->get('role'));
            });
        }

        $users = $query->orderBy('created_at', 'desc')
                      ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $users->items(),
            'current_page' => $users->currentPage(),
            'last_page' => $users->lastPage(),
            'per_page' => $users->perPage(),
            'total' => $users->total(),
            'from' => $users->firstItem(),
            'to' => $users->lastItem(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string|max:20',
            'password' => 'required|string|min:6',
            'organization_id' => 'required|exists:organizations,id',
            'role_ids' => 'required|array',
            'role_ids.*' => 'exists:roles,id',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'organization_id' => $request->organization_id,
            'status' => $request->get('status', 1)
        ]);

        // 分配角色
        if ($request->role_ids) {
            $roles = Role::whereIn('id', $request->role_ids)->get();
            $user->assignRole($roles);
        }

        $user->load(['organization', 'roles']);

        return response()->json([
            'success' => true,
            'message' => '用户创建成功',
            'data' => $user
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user): JsonResponse
    {
        $user->load(['organization', 'roles', 'permissions']);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'organization' => $user->organization,
                'roles' => $user->getRoleNames(),
                'permissions' => $user->getAllPermissions()->pluck('name'),
                'status' => $user->status,
                'last_login_at' => $user->last_login_at,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
            ]
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'organization_id' => 'required|exists:organizations,id',
            'password' => 'nullable|string|min:6',
            'role_ids' => 'nullable|array',
            'role_ids.*' => 'exists:roles,id',
        ]);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'organization_id' => $request->organization_id,
            'status' => $request->get('status', $user->status)
        ];

        // 如果提供了新密码，则更新密码
        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        // 更新角色
        if ($request->has('role_ids')) {
            $user->syncRoles([]);
            if ($request->role_ids) {
                $roles = Role::whereIn('id', $request->role_ids)->get();
                $user->assignRole($roles);
            }
        }

        $user->load(['organization', 'roles']);

        return response()->json([
            'success' => true,
            'message' => '用户更新成功',
            'data' => $user
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user): JsonResponse
    {
        // 防止删除当前登录用户
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => '不能删除当前登录用户'
            ], 422);
        }

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => '用户删除成功'
        ]);
    }

    /**
     * 切换用户状态
     */
    public function toggleStatus(User $user): JsonResponse
    {
        // 防止禁用当前登录用户
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => '不能禁用当前登录用户'
            ], 422);
        }

        $user->update(['status' => !$user->status]);

        return response()->json([
            'success' => true,
            'message' => $user->status ? '用户已启用' : '用户已禁用',
            'data' => ['status' => $user->status]
        ]);
    }

    /**
     * 分配角色
     */
    public function assignRole(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'role' => 'required|exists:roles,name'
        ]);

        $user->assignRole($request->role);

        return response()->json([
            'success' => true,
            'message' => '角色分配成功',
            'data' => ['roles' => $user->getRoleNames()]
        ]);
    }

    /**
     * 撤销角色
     */
    public function revokeRole(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'role' => 'required|exists:roles,name'
        ]);

        $user->removeRole($request->role);

        return response()->json([
            'success' => true,
            'message' => '角色撤销成功',
            'data' => ['roles' => $user->getRoleNames()]
        ]);
    }

    /**
     * 批量导入用户
     */
    public function import(Request $request): JsonResponse
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv'
        ]);

        try {
            $file = $request->file('file');
            $extension = $file->getClientOriginalExtension();

            if ($extension === 'csv') {
                $data = $this->parseCsvFile($file);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '暂时只支持CSV格式文件'
                ], 400);
            }

            $importedCount = 0;
            $errors = [];

            foreach ($data as $index => $row) {
                try {
                    // 跳过标题行
                    if ($index === 0) continue;

                    if (count($row) < 4) {
                        $errors[] = "第" . ($index + 1) . "行：数据不完整";
                        continue;
                    }

                    $name = trim($row[0]);
                    $email = trim($row[1]);
                    $password = trim($row[2]);
                    $organizationCode = trim($row[3]);
                    $roleName = isset($row[4]) ? trim($row[4]) : 'data_entry';

                    // 验证必填字段
                    if (empty($name) || empty($email) || empty($password)) {
                        $errors[] = "第" . ($index + 1) . "行：姓名、邮箱、密码不能为空";
                        continue;
                    }

                    // 验证邮箱格式
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = "第" . ($index + 1) . "行：邮箱格式不正确";
                        continue;
                    }

                    // 检查邮箱是否已存在
                    if (User::where('email', $email)->exists()) {
                        $errors[] = "第" . ($index + 1) . "行：邮箱 {$email} 已存在";
                        continue;
                    }

                    // 查找组织
                    $organization = null;
                    if (!empty($organizationCode)) {
                        $organization = \App\Models\Organization::where('code', $organizationCode)->first();
                        if (!$organization) {
                            $errors[] = "第" . ($index + 1) . "行：找不到组织编码 {$organizationCode}";
                            continue;
                        }
                    }

                    // 验证角色
                    $role = Role::where('name', $roleName)->first();
                    if (!$role) {
                        $errors[] = "第" . ($index + 1) . "行：角色 {$roleName} 不存在";
                        continue;
                    }

                    // 创建用户
                    $user = User::create([
                        'name' => $name,
                        'email' => $email,
                        'password' => Hash::make($password),
                        'organization_id' => $organization ? $organization->id : null,
                        'status' => 1,
                    ]);

                    // 分配角色
                    $user->assignRole($role);

                    $importedCount++;
                } catch (\Exception $e) {
                    $errors[] = "第" . ($index + 1) . "行：" . $e->getMessage();
                }
            }

            return response()->json([
                'success' => true,
                'imported_count' => $importedCount,
                'errors' => $errors,
                'message' => "成功导入 {$importedCount} 个用户" . (count($errors) > 0 ? "，" . count($errors) . " 个错误" : '')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导入失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 解析CSV文件
     */
    private function parseCsvFile($file): array
    {
        $data = [];
        $handle = fopen($file->getPathname(), 'r');

        if ($handle !== false) {
            while (($row = fgetcsv($handle, 1000, ',')) !== false) {
                $data[] = $row;
            }
            fclose($handle);
        }

        return $data;
    }
}
