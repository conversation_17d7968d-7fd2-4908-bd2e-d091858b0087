<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- 模态框内容 -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <!-- 头部 -->
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              表单预览 - {{ template.name }}
            </h3>
            <button
              @click="$emit('close')"
              class="text-gray-400 hover:text-gray-600"
            >
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- 表单信息 -->
          <div class="mb-6 p-4 bg-gray-50 rounded-lg">
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="font-medium text-gray-700">模板名称：</span>
                <span class="text-gray-900">{{ template.name }}</span>
              </div>
              <div>
                <span class="font-medium text-gray-700">分类：</span>
                <span class="text-gray-900">{{ template.category }}</span>
              </div>
              <div class="col-span-2">
                <span class="font-medium text-gray-700">描述：</span>
                <span class="text-gray-900">{{ template.description || '无描述' }}</span>
              </div>
              <div>
                <span class="font-medium text-gray-700">字段数量：</span>
                <span class="text-gray-900">{{ template.config.fields.length }}</span>
              </div>
            </div>
          </div>

          <!-- 表单预览 -->
          <div class="max-h-96 overflow-y-auto">
            <form class="space-y-6" @submit.prevent>
              <div
                v-for="field in template.config.fields"
                :key="field.id"
                class="space-y-2"
              >
                <!-- 字段标签 -->
                <label class="block text-sm font-medium text-gray-700">
                  {{ field.label }}
                  <span v-if="field.required" class="text-red-500">*</span>
                </label>

                <!-- 字段描述 -->
                <p v-if="field.description" class="text-xs text-gray-500 -mt-1">
                  {{ field.description }}
                </p>

                <!-- 单行文本 -->
                <input
                  v-if="field.type === 'text'"
                  type="text"
                  :placeholder="field.placeholder"
                  :required="field.required"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />

                <!-- 多行文本 -->
                <textarea
                  v-else-if="field.type === 'textarea'"
                  :placeholder="field.placeholder"
                  :rows="field.rows || 3"
                  :required="field.required"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                ></textarea>

                <!-- 数字输入 -->
                <input
                  v-else-if="field.type === 'number'"
                  type="number"
                  :placeholder="field.placeholder"
                  :required="field.required"
                  :min="field.validation?.min"
                  :max="field.validation?.max"
                  :step="field.validation?.precision ? Math.pow(10, -field.validation.precision) : 1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />

                <!-- 下拉选择 -->
                <select
                  v-else-if="field.type === 'select'"
                  :required="field.required"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">请选择</option>
                  <option v-for="option in field.options" :key="option" :value="option">
                    {{ option }}
                  </option>
                </select>

                <!-- 多选框 -->
                <div v-else-if="field.type === 'checkbox'" class="space-y-2">
                  <div v-for="option in field.options" :key="option" class="flex items-center">
                    <input
                      type="checkbox"
                      :id="`preview_${field.id}_${option}`"
                      :value="option"
                      class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label :for="`preview_${field.id}_${option}`" class="ml-2 text-sm text-gray-700">
                      {{ option }}
                    </label>
                  </div>
                </div>

                <!-- 单选框 -->
                <div v-else-if="field.type === 'radio'" class="space-y-2">
                  <div v-for="option in field.options" :key="option" class="flex items-center">
                    <input
                      type="radio"
                      :name="`preview_${field.id}`"
                      :id="`preview_${field.id}_${option}`"
                      :value="option"
                      class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <label :for="`preview_${field.id}_${option}`" class="ml-2 text-sm text-gray-700">
                      {{ option }}
                    </label>
                  </div>
                </div>

                <!-- 日期选择 -->
                <input
                  v-else-if="field.type === 'date'"
                  type="date"
                  :required="field.required"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />

                <!-- 文件上传 -->
                <div v-else-if="field.type === 'file'">
                  <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <div class="mt-4">
                      <label class="cursor-pointer">
                        <span class="text-sm text-blue-600 hover:text-blue-500">点击上传文件</span>
                        <input
                          type="file"
                          class="hidden"
                          :required="field.required"
                          :accept="field.validation?.accept"
                          :multiple="field.validation?.multiple"
                        />
                      </label>
                      <p class="text-xs text-gray-500 mt-1">或拖拽文件到此处</p>
                    </div>
                    
                    <!-- 文件限制说明 -->
                    <div v-if="field.validation" class="mt-2 text-xs text-gray-500">
                      <div v-if="field.validation.accept">
                        支持格式：{{ field.validation.accept }}
                      </div>
                      <div v-if="field.validation.maxSize">
                        最大大小：{{ field.validation.maxSize }}MB
                      </div>
                      <div v-if="field.validation.multiple">
                        支持多文件上传
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 验证提示 -->
                <div v-if="field.validation" class="text-xs text-gray-500">
                  <div v-if="field.validation.minLength || field.validation.maxLength">
                    长度限制：{{ field.validation.minLength || 0 }} - {{ field.validation.maxLength || '无限制' }} 个字符
                  </div>
                  <div v-if="field.validation.min !== undefined || field.validation.max !== undefined">
                    数值范围：{{ field.validation.min || '无限制' }} - {{ field.validation.max || '无限制' }}
                  </div>
                </div>
              </div>

              <!-- 提交按钮 -->
              <div class="pt-4 border-t">
                <button
                  type="submit"
                  class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                >
                  提交表单
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="$emit('close')"
            type="button"
            class="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface FormField {
  id: string;
  type: string;
  label: string;
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: any;
  description?: string;
  rows?: number;
}

interface FormTemplate {
  name: string;
  category: string;
  description: string;
  config: {
    fields: FormField[];
  };
}

interface Props {
  template: FormTemplate;
}

defineProps<Props>();
defineEmits<{
  close: [];
}>();
</script>

<style scoped>
/* 自定义滚动条样式 */
.max-h-96::-webkit-scrollbar {
  width: 6px;
}

.max-h-96::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
