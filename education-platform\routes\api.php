<?php

use App\Http\Controllers\Api\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 公开路由
Route::prefix('v1')->group(function () {
    // 认证相关
    Route::post('/login', [AuthController::class, 'login']);
    
    // 需要认证的路由
    Route::middleware('auth:sanctum')->group(function () {
        // 用户认证
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/me', [AuthController::class, 'me']);
        Route::post('/change-password', [AuthController::class, 'changePassword']);
        
        // 组织架构管理
        Route::apiResource('organizations', \App\Http\Controllers\Api\OrganizationController::class);
        Route::get('organizations/{organization}/children', [\App\Http\Controllers\Api\OrganizationController::class, 'children']);
        Route::get('organizations/tree', [\App\Http\Controllers\Api\OrganizationController::class, 'tree']);
        Route::post('organizations/import', [\App\Http\Controllers\Api\OrganizationController::class, 'import']);
        
        // 表单模板管理
        Route::apiResource('form-templates', \App\Http\Controllers\Api\FormTemplateController::class);
        Route::post('form-templates/{formTemplate}/publish', [\App\Http\Controllers\Api\FormTemplateController::class, 'publish']);
        Route::post('form-templates/{formTemplate}/archive', [\App\Http\Controllers\Api\FormTemplateController::class, 'archive']);
        Route::post('form-templates/{formTemplate}/duplicate', [\App\Http\Controllers\Api\FormTemplateController::class, 'duplicate']);
        Route::get('form-templates/{formTemplate}/preview', [\App\Http\Controllers\Api\FormTemplateController::class, 'preview']);
        
        // 统计任务管理
        Route::apiResource('statistics-tasks', \App\Http\Controllers\Api\StatisticsTaskController::class);
        Route::post('statistics-tasks/{statisticsTask}/publish', [\App\Http\Controllers\Api\StatisticsTaskController::class, 'publish']);
        Route::get('statistics-tasks/{statisticsTask}/progress', [\App\Http\Controllers\Api\StatisticsTaskController::class, 'progress']);
        Route::post('statistics-tasks/{statisticsTask}/remind', [\App\Http\Controllers\Api\StatisticsTaskController::class, 'remind']);
        
        // 数据提交管理
        Route::apiResource('data-submissions', \App\Http\Controllers\Api\DataSubmissionController::class);
        Route::post('data-submissions/{dataSubmission}/submit', [\App\Http\Controllers\Api\DataSubmissionController::class, 'submit']);
        Route::post('data-submissions/{dataSubmission}/approve', [\App\Http\Controllers\Api\DataSubmissionController::class, 'approve']);
        Route::post('data-submissions/{dataSubmission}/reject', [\App\Http\Controllers\Api\DataSubmissionController::class, 'reject']);
        
        // 用户管理
        Route::apiResource('users', \App\Http\Controllers\Api\UserController::class);
        Route::post('users/{user}/toggle-status', [\App\Http\Controllers\Api\UserController::class, 'toggleStatus']);
        Route::post('users/import', [\App\Http\Controllers\Api\UserController::class, 'import']);
        
        // 角色权限管理
        Route::apiResource('roles', \App\Http\Controllers\Api\RoleController::class);
        Route::apiResource('permissions', \App\Http\Controllers\Api\PermissionController::class);
        Route::post('users/{user}/assign-role', [\App\Http\Controllers\Api\UserController::class, 'assignRole']);
        Route::post('users/{user}/revoke-role', [\App\Http\Controllers\Api\UserController::class, 'revokeRole']);
    });
});
