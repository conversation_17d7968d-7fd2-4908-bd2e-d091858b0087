# 综合教育数据统计平台 (Integrated Education Platform)

## 项目简介

这是一个基于Laravel和Vue.js开发的综合教育数据统计平台，旨在为教育管理部门提供高效的数据收集、统计和分析功能。

## 主要功能

### 🏢 组织架构管理
- 支持多级教育组织架构（省/市/区县/学校）
- 灵活的组织层级配置
- 组织信息的增删改查

### 👥 用户权限管理
- 基于角色的权限控制系统
- 支持多角色分配
- 细粒度的权限管理

### 📋 表单模板系统
- 可视化表单设计器
- 支持多种字段类型（文本、数字、选择、日期等）
- 表单模板的复用和版本管理

### 📊 统计任务管理
- 灵活的统计任务创建和分发
- 支持定时任务和周期性统计
- 数据收集进度跟踪

### 📈 数据分析与报表
- 实时数据统计和分析
- 多维度数据展示
- 可导出的统计报表

## 技术栈

### 后端
- **Laravel 11** - PHP Web框架
- **MySQL** - 数据库
- **Laravel Sanctum** - API认证
- **Spatie Laravel Permission** - 权限管理

### 前端
- **Vue.js 3** - 前端框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Vite** - 构建工具

## 安装和部署

### 环境要求
- PHP >= 8.2
- Node.js >= 16
- MySQL >= 8.0
- Composer

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/jyjzbk/gcjytjst.git
cd gcjytjst/education-platform
```

2. **安装PHP依赖**
```bash
composer install
```

3. **安装前端依赖**
```bash
npm install
```

4. **环境配置**
```bash
cp .env.example .env
php artisan key:generate
```

5. **数据库配置**
编辑 `.env` 文件，配置数据库连接信息：
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=education_platform
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

6. **数据库迁移和填充**
```bash
php artisan migrate
php artisan db:seed
```

7. **构建前端资源**
```bash
npm run build
```

8. **启动服务**
```bash
php artisan serve
```

## 项目结构

```
education-platform/
├── app/                    # Laravel应用核心代码
│   ├── Http/Controllers/   # 控制器
│   ├── Models/            # 数据模型
│   └── Console/Commands/  # 命令行工具
├── database/              # 数据库相关
│   ├── migrations/        # 数据库迁移文件
│   └── seeders/          # 数据填充文件
├── resources/             # 前端资源
│   ├── js/               # Vue.js应用
│   └── views/            # Blade模板
├── routes/               # 路由定义
└── tests/               # 测试文件
```

## 开发指南

### API文档
项目提供RESTful API接口，主要包括：
- 用户认证：`/api/auth/*`
- 组织管理：`/api/organizations/*`
- 表单模板：`/api/form-templates/*`
- 统计任务：`/api/statistics-tasks/*`
- 数据提交：`/api/data-submissions/*`

### 测试
```bash
# 运行PHP单元测试
php artisan test

# 运行前端测试
npm run test
```

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者：jyjzbk
- 邮箱：<EMAIL>
- GitHub：[@jyjzbk](https://github.com/jyjzbk)
